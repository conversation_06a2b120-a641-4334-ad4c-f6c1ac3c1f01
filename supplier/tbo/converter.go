package tbo

import (
	"hotel/common/types"
	"strconv"
	"time"

	"hotel/common/i18n"
	"hotel/common/money"
	geoDomain "hotel/geography/domain"
	"hotel/supplier/domain"
	"hotel/supplier/tbo/model"
)

// convertHotelsToDomain 将 TBO 酒店列表转换为领域模型
func (s *TboClient) convertHotelsToDomain(hotels []model.HotelResult) []domain.SupplierHotel {
	var result []domain.SupplierHotel
	for _, hotel := range hotels {
		supplierHotel := domain.SupplierHotel{
			SupplierHotelId: hotel.HotelCode,
			Name:            i18n.I18N{En: hotel.HotelCode}, // 使用 HotelCode 作为名称，因为 HotelResult 没有 HotelName 字段
			Supplier:        domain.Supplier_TBO,
		}

		// 设置静态信息 - 由于 HotelResult 结构简单，设置默认值
		supplierHotel.Star = 0 // 默认星级
		supplierHotel.Phone = ""
		supplierHotel.Address = i18n.I18N{En: ""}
		supplierHotel.LatlngCoordinator = geoDomain.LatlngCoordinator{}

		// 设置动态信息
		supplierHotel.IsAvailable = true

		// 转换房间信息
		supplierHotel.Rooms = s.convertRoomsToDomain(hotel.Rooms, hotel.HotelCode)

		result = append(result, supplierHotel)
	}
	return result
}

// convertRoomsToDomain 将 TBO 房间信息转换为领域模型
func (s *TboClient) convertRoomsToDomain(rooms []model.Room, hotelID string) []*domain.Room {
	var result []*domain.Room
	for _, room := range rooms {
		domainRoom := &domain.Room{
			HotelRoomStaticProfile: domain.HotelRoomStaticProfile{
				RoomTypeID:      room.BookingCode, // 使用 BookingCode 作为房间类型ID
				SupplierHotelId: hotelID,
				RoomName:        i18n.I18N{En: room.Name[0]}, // 使用第一个名称
			},
			HotelRoomDynamicProfile: domain.HotelRoomDynamicProfile{
				IsAvailable: true,
			},
		}

		// 设置吸烟偏好 - Room 结构中没有 SmokingPreference 字段，跳过

		result = append(result, domainRoom)
	}
	return result
}

// convertHotelRatesToDomain 将 TBO 酒店价格转换为领域模型
func (s *TboClient) convertHotelRatesToDomain(hotels []model.HotelResult) []domain.Room {
	var result []domain.Room
	for _, hotel := range hotels {
		for _, room := range hotel.Rooms {
			// 构建产品 ID
			productID := s.buildProductID(0, hotel.HotelCode, hotel.HotelCode, room.BookingCode, room.Name[0], room.BookingCode)

			domainRoom := domain.Room{
				HotelRoomStaticProfile: domain.HotelRoomStaticProfile{
					RoomTypeID:      room.BookingCode,
					SupplierHotelId: hotel.HotelCode,
					RoomName:        i18n.I18N{En: room.Name[0]},
				},
				HotelRoomDynamicProfile: domain.HotelRoomDynamicProfile{
					IsAvailable: true, // TBO 返回的房间都是可用的
				},
				Rates: []domain.RoomRatePkg{
					{
						RatePkgId: productID,
						RatePlan: domain.RatePlan{
							RatePlanId: room.BookingCode, // 使用 BookingCode 作为 RatePlanId
							Meal: domain.Meal{
								Type:        domain.MealTypeNone,
								Description: "",
							},
							Inventory: 1, // TBO 不提供库存信息，默认为1
						},
						Rate: domain.Rate{
							FinalRate: money.Money{Amount: room.TotalFare}, // 使用 TotalFare 作为价格
						},
						Available: true,
					},
				},
			}
			result = append(result, domainRoom)
		}
	}
	return result
}

// convertCancelPolicies 转换取消政策
func (s *TboClient) convertCancelPolicies(policies []model.CancelPolicy) domain.CancelPolicy {
	if len(policies) == 0 {
		return domain.CancelPolicy{
			Policies:       []domain.CancelPolicyItem{},
			RefundableMode: domain.RefundableModeNo,
			Refundable:     false,
		}
	}

	var policyItems []domain.CancelPolicyItem
	for _, policy := range policies {
		// 解析时间字符串
		fromTime, _ := time.Parse("2006-01-02", policy.FromDate)
		toTime := fromTime // 由于 TBO 模型没有 ToDate，使用 FromDate

		policyItem := domain.CancelPolicyItem{
			Type:     domain.DeductionType_TimeLimit,
			TypeName: "TimeLimit", // 使用固定值，因为TBO模型没有Type字段
			CancelFee: money.Money{
				Amount:   policy.CancellationCharge * 100, // 修复：使用float64
				Currency: "USD",                           // 默认货币，因为 TBO 模型没有 Currency 字段
			},
			DeductionTimeWindow: types.TimeWindow{
				Start: fromTime,
				End:   toTime,
			},
		}
		policyItems = append(policyItems, policyItem)
	}

	return domain.CancelPolicy{
		Policies:       policyItems,
		RefundableMode: domain.RefundableModePartially,
		Refundable:     true,
	}
}

// convertGuestInfo 转换客人信息
func (s *TboClient) convertGuestInfo(booker domain.Booker, guests []domain.Guest) model.GuestInfo {
	// 构建客人信息
	var paxInfo []model.PaxInfo

	// 添加预订人信息 - 修复：Booker没有Title字段
	paxInfo = append(paxInfo, model.PaxInfo{
		Title:     "", // 移除对Title字段的引用
		FirstName: booker.FirstName,
		LastName:  booker.LastName,
		PaxType:   "Adult",
		LeadPax:   true,
	})

	// 添加其他客人信息
	for _, guest := range guests {
		paxType := "Adult"
		if guest.Age < 12 {
			paxType = "Child"
		}
		paxInfo = append(paxInfo, model.PaxInfo{
			Title:     "", // 移除对Title字段的引用
			FirstName: guest.FirstName,
			LastName:  guest.LastName,
			PaxType:   paxType,
			Age:       int(guest.Age),
		})
	}

	return model.GuestInfo{
		ContactNo: booker.Phone.Number, // 修复：使用Phone.Number
		Email:     booker.Email,
		FirstName: booker.FirstName,
		LastName:  booker.LastName,
		PaxRooms: []model.BookingPaxRoom{
			{
				Adults:   1, // 修复：移除对不存在字段的引用
				Children: 0,
				PaxInfo:  paxInfo,
			},
		},
	}
}

// buildProductID 构建产品ID
func (s *TboClient) buildProductID(resultIndex int, hotelCode, hotelName, roomTypeCode, roomTypeName, ratePlanCode string) string {
	return strconv.Itoa(resultIndex) + "|" + hotelCode + "|" + hotelName + "|" + roomTypeCode + "|" + roomTypeName + "|" + ratePlanCode
}
