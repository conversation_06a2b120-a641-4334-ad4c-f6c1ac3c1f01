package domain

import (
	"hotel/common/types"
	"time"

	"hotel/common/i18n"
	"hotel/common/money"
)

type FacilityGroup struct {
	GroupID          int32
	GroupTypeName    string
	GroupName        string
	FacilityLimit    FacilityLimitEnum
	FacilityItemList []*FacilityItem
}

type FacilityItem struct {
	FacilityItemName string
	FacilityID       int32
	Chargeable       bool
	ChargeableDesc   string
	FacilityLimit    FacilityLimitEnum
}

type HotelPolicy struct {
	MealPolicy                *MealPolicy
	ArrivalAndDeparturePolicy *ArrivalAndDeparturePolicy
}

type MealPolicy struct {
	BreakfastStyle string
	BreakfastPrice string
}

type ArrivalAndDeparturePolicy struct {
	ArrivalDesc   string
	DepartureDesc string

	ArrivalFrom   string
	ArrivalTo     string
	DepartureFrom string
	DepartureTo   string
}

type LadderDeductionInfo struct {
	DeductionType         CancelPolicyType
	LadderDeductionDetail *LadderDeductionDetail
}

type LadderDeductionDetail struct {
	StartDeductTime *LocationTime // "1970-01-01 08:00:00"
	EndDeductTime   *LocationTime
	OriginPrice     float64
	OriginCurrency  string
	Price           float64
	PriceCurrency   string
	DeductionRatio  float64
}

type BedTypeInfo struct {
	Count            int
	ChildBedTypeName string
	ChildBedType     ChildBedType
}

type GroupedBedTypeInfo struct {
	BedType           i18n.I18N
	AvailableBedCount []int64
}

type TaxDetail struct {
	TaxID       string
	TaxPrice    float64
	TaxTypeName string
}

type LocationTime struct {
	ZoneID        string
	LocationName  string
	LocalDateTime time.Time
}

type RoomDayDetail struct {
	RoomStatusDetail RoomStatus // 房间详情状态
	RoomStatus       RoomStatus // 有无房状态
	EffectDate       time.Time  // 有效日期
	Price            float64    // 价格
	Currency         string
	BreakfastCount   int32
	BreakfastDesc    string
	BreakfastDescEn  string
	DayHoldRoom      int32
	OriginCurrency   string  // 目前这个字段没用
	OriginPrice      float64 // 目前这个字段没用
}

type RoomStatus int

const (
	RoomStatusY  RoomStatus = iota + 1 // 有房
	RoomtStatusN                       // 满房 或 无房
	RoomStausW                         // 无房
	RoomStatusU                        // 未知
	RoomStatusL                        // 不可超
	G                                  // 良好
	S                                  // 紧张
)

type BedType int

const (
	KingBed BedType = iota + 1
	TwinBed
	SingleBed
	MultiBed
)

type ChildBedType int

const (
	Bunk ChildBedType = iota + 1
	Single
	Double
	Round
	Queen
	Capsule
	SemiDouble
	Tatami
	Water
	Sofa
	OnDol
	King
	Floor
)

func (p ChildBedType) String() string {
	switch p {
	case Bunk:
		return "Bunk"
	case Single:
		return "Single"
	case Double:
		return "Double"
	case Round:
		return "Round"
	case Queen:
		return "Queen"
	case Capsule:
		return "Capsule"
	case SemiDouble:
		return "SemiDouble"
	case Tatami:
		return "Tatami"
	case Water:
		return "Water"
	case Sofa:
		return "Sofa"
	case OnDol:
		return "OnDol"
	case King:
		return "King"
	case Floor:
		return "Floor"
	}
	return "<UNSET>"
}

func (p ChildBedType) OriginalBedID() string {
	switch p {
	case Bunk:
		return "657"
	case Single:
		return "370"
	case Double:
		return "365"
	case Round:
		return "366"
	case Queen:
		return "4060"
	case Capsule:
		return "851"
	case SemiDouble:
		return "4059"
	case Tatami:
		return "368"
	case Water:
		return "367"
	case Sofa:
		return "1144"
	case OnDol:
		return "369"
	case King:
		return "4061"
	case Floor:
		return "6974"
	}
	return "<UNSET>"
}

type RoomPayType int

const (
	RoomPayTypePP    RoomPayType = iota + 1 // 预付
	RoomPayTypeFG                           // 现付
	RoomPayTypePH                           // 预付到酒店
	RoomPayTypeUseFG                        // 现转预房型-预付
)

type BalanceType int

const (
	BalanceTypePP    BalanceType = iota + 1 // 预付
	BalanceTypeFG                           // 现付
	BalanceTypePH                           // 预付到酒店
	BalanceTypeUseFG                        // 现转预房型-预付
)

// CancelPolicyItem 取消政策
type CancelPolicyItem struct {
	Type                CancelPolicyType `json:"type"`
	TypeName            string           `json:"TypeName"`
	CancelFee           money.Money      `json:"cancelFee"`           // if cancelFee is not zero, the money would not be returned back after cancellation
	DeductionTimeWindow types.TimeWindow `json:"deductionTimeWindow"` // limited time window for cancellation by DeductionType_TimeLimit
}

type RoomMealInfo struct {
	MealType MealType `thrift:"MealType,1" json:"mealType"`

	RoomMealDesc []string `thrift:"RoomMealDesc,2" json:"roomMealDesc"`

	MealRemark string `thrift:"MealRemark,3" json:"mealRemark"`

	DailyMealInfoList []*DailyMealInfo `thrift:"DailyMealInfoList,4" json:"dailyMealInfoList"`
}

type DailyMealInfo struct {
	EffectDate string `thrift:"EffectDate,1" json:"effectDate"`

	DailyMealDescList []string `thrift:"DailyMealDescList,2" json:"dailyMealDescList"`

	Meals int64 `thrift:"Meals,3" json:"meals"`
}
