package mysql

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/bizerr"
	"hotel/common/log"
	"hotel/common/pagehelper"
	"hotel/common/types"
	supplierDomain "hotel/supplier/domain"

	jsqlx "github.com/jmoiron/sqlx"

	"hotel/common/utils"
	geoDomain "hotel/geography/domain"

	"github.com/zeromicro/go-zero/core/stores/sqlc"
)

type (
	Hotel struct {
		Id                    int64              `db:"id"`
		CityRegionId          int64              `db:"city_region_id"`
		MasterSupplier        int64              `db:"master_supplier"`
		Supplier1HotelId      string             `db:"supplier_1_id"`
		Supplier2HotelId      string             `db:"supplier_2_id"`
		Supplier3HotelId      string             `db:"supplier_3_id"`
		Supplier4HotelId      string             `db:"supplier_4_id"`
		Supplier5HotelId      string             `db:"supplier_5_id"`
		Supplier6HotelId      string             `db:"supplier_6_id"`
		Supplier7HotelId      string             `db:"supplier_7_id"`
		Supplier8HotelId      string             `db:"supplier_8_id"`
		Supplier9HotelId      string             `db:"supplier_9_id"`
		Supplier10HotelId     string             `db:"supplier_10_id"`
		Supplier11HotelId     string             `db:"supplier_11_id"`
		Supplier12HotelId     string             `db:"supplier_12_id"`
		Supplier13HotelId     string             `db:"supplier_13_id"`
		Supplier14HotelId     string             `db:"supplier_14_id"`
		Supplier15HotelId     string             `db:"supplier_15_id"`
		Supplier16HotelId     string             `db:"supplier_16_id"`
		Supplier17HotelId     string             `db:"supplier_17_id"`
		Supplier18HotelId     string             `db:"supplier_18_id"`
		Supplier19HotelId     string             `db:"supplier_19_id"`
		Supplier20HotelId     string             `db:"supplier_20_id"`
		Supplier21HotelId     string             `db:"supplier_21_id"`
		Supplier22HotelId     string             `db:"supplier_22_id"`
		Supplier23HotelId     string             `db:"supplier_23_id"`
		Supplier24HotelId     string             `db:"supplier_24_id"`
		Supplier25HotelId     string             `db:"supplier_25_id"`
		Supplier26HotelId     string             `db:"supplier_26_id"`
		Supplier27HotelId     string             `db:"supplier_27_id"`
		Supplier28HotelId     string             `db:"supplier_28_id"`
		Supplier29HotelId     string             `db:"supplier_29_id"`
		Supplier30HotelId     string             `db:"supplier_30_id"`
		GoogleGeo             geoDomain.GeoPoint `db:"google_geo"`
		GaodeGeo              geoDomain.GeoPoint `db:"gaode_geo"`
		MinPrice              float64            `db:"min_price"`
		Rating                float64            `db:"rating"`
		Star                  float64            `db:"star"`
		BrandId               int64              `db:"brand_id"`
		StaticProfile         string             `db:"static_profile"`
		CreateTime            time.Time          `db:"create_time"`
		UpdateTime            time.Time          `db:"update_time"`
		LastRefreshStaticTime time.Time          `db:"last_refresh_static_time"`
		IsDeleted             int64              `db:"is_deleted"` // 删除状态标记
	}

	HotelModel struct {
		sqlx.SqlConn
		table string
	}
)

func (m *HotelModel) HardDelete(ctx context.Context, ids []int64) error {
	query := fmt.Sprintf("DELETE FROM %s WHERE id in (?)", m.table)
	query, args, err := jsqlx.In(query, ids)
	if err != nil {
		return err
	}
	_, err = m.ExecCtx(ctx, query, args...)
	return err
}

func (m *HotelModel) SoftDelete(ctx context.Context, ids []int64) error {
	query := fmt.Sprintf(
		"UPDATE %s SET is_deleted = ? WHERE id in (?)",
		m.table,
	)
	query, args, err := jsqlx.In(query, 1, ids)
	if err != nil {
		return err
	}
	_, err = m.ExecCtx(ctx, query, args...)
	return err
}

func (m *HotelModel) FindByID(ctx context.Context, id int64) (*Hotel, error) {
	var resp Hotel
	query := fmt.Sprintf(`SELECT %s
		FROM %s WHERE id = ?`, fieldsHotel, m.table)
	err := m.QueryRowCtx(ctx, &resp, query, id)
	switch {
	case err == nil:
		return &resp, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// INSERT INTO hotel (
//
//		google_geo,gaode_geo,
//		id,city_region_id,
//		brand_id,star,rating,
//		min_price,static_profile,
//		master_supplier,supplier_1_id,supplier_2_id,supplier_3_id,
//		supplier_4_id,supplier_5_id,supplier_6_id,supplier_7_id,supplier_8_id,supplier_9_id,supplier_10_id,supplier_11_id,supplier_12_id,supplier_13_id,supplier_14_id,supplier_15_id,supplier_16_id,supplier_17_id,supplier_18_id,supplier_19_id,supplier_20_id,create_time,update_time,is_deleted
//	) VALUES
func (m *HotelModel) BatchInsert(ctx context.Context, in []*Hotel) error {
	insertSQL := fmt.Sprintf(`INSERT INTO %s (
		%s
	) VALUES `, m.table, fieldsHotelWithoutTime)

	args := make([]interface{}, 0, len(in))
	for i, data := range in {
		if i != 0 {
			insertSQL += ","
		}
		valuesSQL := fmt.Sprintf(`(ST_GeomFromText('POINT(%.6f %.6f)', 4326), ST_GeomFromText('POINT(%.6f %.6f)', 4326),
		%s
	)`, data.GoogleGeo.Point.Lat(), data.GoogleGeo.Point.Lon(), data.GaodeGeo.Point.Lat(), data.GaodeGeo.Point.Lon(), buildHotelArgsPlaceholder())
		insertSQL += valuesSQL
		args = append(args, buildArgsFromHotelForInsert(data)...)
	}

	// 自动生成 ON DUPLICATE KEY UPDATE 语句，排除 id 和 create_time 字段
	updateFields := []string{
		// 字段顺序与 fieldsHotelWithoutTime 保持一致，排除 id, create_time
		"google_geo", "gaode_geo", "city_region_id", "brand_id", "star", "rating", "min_price", "static_profile", "master_supplier",
		"supplier_1_id", "supplier_2_id", "supplier_3_id", "supplier_4_id", "supplier_5_id", "supplier_6_id", "supplier_7_id", "supplier_8_id", "supplier_9_id", "supplier_10_id", "supplier_11_id", "supplier_12_id", "supplier_13_id", "supplier_14_id", "supplier_15_id", "supplier_16_id", "supplier_17_id", "supplier_18_id", "supplier_19_id", "supplier_20_id", "supplier_21_id", "supplier_22_id", "supplier_23_id", "supplier_24_id", "supplier_25_id", "supplier_26_id", "supplier_27_id", "supplier_28_id", "supplier_29_id", "supplier_30_id", "is_deleted",
	}
	updateSQL := make([]string, 0, len(updateFields))
	for _, f := range updateFields {
		updateSQL = append(updateSQL, fmt.Sprintf("%s=VALUES(%s)", f, f))
	}
	insertSQL += " ON DUPLICATE KEY UPDATE " + strings.Join(updateSQL, ", ")

	res, err := m.ExecCtx(ctx, insertSQL, args...)
	if err != nil {
		return err
	}
	affected, _ := res.RowsAffected()
	log.Debugc(ctx, "Insert %d rows by sql(%s)", affected, insertSQL)
	return err
}

var (
	fieldsHotel                 = `last_refresh_static_time,google_geo,gaode_geo,id,city_region_id,brand_id,star,rating,min_price,static_profile,master_supplier,supplier_1_id,supplier_2_id,supplier_3_id,supplier_4_id,supplier_5_id,supplier_6_id,supplier_7_id,supplier_8_id,supplier_9_id,supplier_10_id,supplier_11_id,supplier_12_id,supplier_13_id,supplier_14_id,supplier_15_id,supplier_16_id,supplier_17_id,supplier_18_id,supplier_19_id,supplier_20_id,supplier_21_id,supplier_22_id,supplier_23_id,supplier_24_id,supplier_25_id,supplier_26_id,supplier_27_id,supplier_28_id,supplier_29_id,supplier_30_id,create_time,update_time,is_deleted`
	fieldsHotelWithoutTime      = `google_geo,gaode_geo,id,city_region_id,brand_id,star,rating,min_price,static_profile,master_supplier,supplier_1_id,supplier_2_id,supplier_3_id,supplier_4_id,supplier_5_id,supplier_6_id,supplier_7_id,supplier_8_id,supplier_9_id,supplier_10_id,supplier_11_id,supplier_12_id,supplier_13_id,supplier_14_id,supplier_15_id,supplier_16_id,supplier_17_id,supplier_18_id,supplier_19_id,supplier_20_id,supplier_21_id,supplier_22_id,supplier_23_id,supplier_24_id,supplier_25_id,supplier_26_id,supplier_27_id,supplier_28_id,supplier_29_id,supplier_30_id,is_deleted`
	countFieldsHotelWithoutTime = len(strings.Split(fieldsHotelWithoutTime, ","))
)

func buildHotelArgsPlaceholder() string {
	n := countFieldsHotelWithoutTime - 2 // 去掉 经纬度字段
	chs := make([]string, 0, n)
	for i := 0; i < n; i++ {
		chs = append(chs, "?")
	}
	return strings.Join(chs, ",")
}
func buildArgsFromHotelForInsert(data *Hotel) []interface{} {
	return []interface{}{data.Id, data.CityRegionId,
		data.BrandId, data.Star, data.Rating,
		data.MinPrice,
		data.StaticProfile,
		data.MasterSupplier,
		data.Supplier1HotelId, data.Supplier2HotelId, data.Supplier3HotelId, data.Supplier4HotelId,
		data.Supplier5HotelId, data.Supplier6HotelId, data.Supplier7HotelId, data.Supplier8HotelId,
		data.Supplier9HotelId, data.Supplier10HotelId, data.Supplier11HotelId, data.Supplier12HotelId,
		data.Supplier13HotelId, data.Supplier14HotelId, data.Supplier15HotelId, data.Supplier16HotelId,
		data.Supplier17HotelId, data.Supplier18HotelId, data.Supplier19HotelId, data.Supplier20HotelId,
		data.Supplier21HotelId, data.Supplier22HotelId, data.Supplier23HotelId, data.Supplier24HotelId,
		data.Supplier25HotelId, data.Supplier26HotelId, data.Supplier27HotelId, data.Supplier28HotelId,
		data.Supplier29HotelId, data.Supplier30HotelId,
		data.IsDeleted}
}

func (m *HotelModel) Insert(ctx context.Context, data *Hotel) error {
	insertSQL := fmt.Sprintf(`INSERT INTO %s (
		%s
	) VALUES (
		ST_GeomFromText('POINT(%.6f %.6f)', 4326), ST_GeomFromText('POINT(%.6f %.6f)', 4326),
		%s
	)`, m.table, fieldsHotelWithoutTime, data.GoogleGeo.Point.Lat(), data.GoogleGeo.Point.Lon(), data.GaodeGeo.Point.Lat(), data.GaodeGeo.Point.Lon(), buildHotelArgsPlaceholder())

	log.Debugc(ctx, "Insert sql(%s)", insertSQL)
	_, err := m.ExecCtx(ctx, insertSQL, buildArgsFromHotelForInsert(data)...)
	return err
}

func (m *HotelModel) FindBySupplierID(ctx context.Context, supplierType int, supplierHotelId string) (*Hotel, error) {
	var h Hotel
	query := fmt.Sprintf("SELECT %s FROM %s WHERE %s = ? LIMIT 1", fieldsHotel, m.table,
		fmt.Sprintf("supplier_%d_id", supplierType))
	err := m.QueryRowCtx(ctx, &h, query, supplierHotelId)
	switch {
	case err == nil:
		return &h, nil
	case errors.Is(err, sqlc.ErrNotFound):
		return nil, bizerr.NotFoundErr
	default:
		return nil, err
	}
}

// ListPage 支持按 regionIds 和 suppliers 分页查询，二者都可为空
// 推荐所有分页列表查询统一用 ListPage，特殊业务如全文检索、地理距离等请用专用方法
func (m *HotelModel) ListPage(ctx context.Context, regionIds types.IDs, pageReq pagehelper.PageReq, suppliers []supplierDomain.Supplier) ([]*Hotel, *pagehelper.PageResp, error) {
	var (
		whereConds []string
		args       []interface{}
		orderBy    string
	)

	if len(regionIds) > 0 {
		whereConds = append(whereConds, "city_region_id in (?)")
		args = append(args, regionIds)
	}

	if len(suppliers) > 0 {
		var supplierConditions []string
		for _, supplier := range suppliers {
			if supplier == supplierDomain.Supplier_UNKNOWN {
				continue
			}
			supplierConditions = append(supplierConditions, fmt.Sprintf("supplier_%d_id != ''", supplier))
		}
		if len(supplierConditions) > 0 {
			whereConds = append(whereConds, "("+strings.Join(supplierConditions, " OR ")+")")
		}
	}

	if pageReq.Cursor > 0 {
		whereConds = append(whereConds, "id > ?")
		args = append(args, pageReq.Cursor)
		orderBy = " ORDER BY id ASC"
	}

	whereSQL := ""
	if len(whereConds) > 0 {
		whereSQL = " WHERE " + strings.Join(whereConds, " AND ")
	}

	baseQuery := fmt.Sprintf("SELECT %s FROM %s%s %s LIMIT ?,?", fieldsHotel, m.table, whereSQL, orderBy)
	args = append(args, pageReq.GetOffset(), pageReq.PageSize)

	query, realArgs, err := jsqlx.In(baseQuery, args...)
	if err != nil {
		return nil, nil, err
	}
	var hs []*Hotel
	err = m.QueryRowsCtx(ctx, &hs, query, realArgs...)
	if err != nil {
		return nil, nil, err
	}
	return hs, &pagehelper.PageResp{
		HasMore: int64(len(hs)) == pageReq.PageSize,
	}, err
}

func (m *HotelModel) SearchByName(ctx context.Context, lang, keyword string, limit int) ([]*Hotel, error) {
	const searchSQL = `SELECT h.* 
		FROM hotel h
		INNER JOIN hotel_name hn ON h.id = hn.hotel_id
		WHERE hn.language = ? 
		AND MATCH(hn.name) AGAINST(? IN BOOLEAN MODE)
		ORDER BY h.rating DESC 
		LIMIT ?`

	var hotels []*Hotel
	err := m.QueryRowsCtx(ctx, &hotels, searchSQL, lang, keyword, limit)
	return hotels, err
}

// FindNearby core feature - 高性能优化版本
func (m *HotelModel) FindNearby(ctx context.Context, latlngCord geoDomain.LatlngCoordinator, radius float64) ([]*Hotel, error) {
	latlng, useGaode := latlngCord.Extract()
	lat, lng := latlng.Lat, latlng.Lng
	geoField := useGaode + "_geo"

	// 高性能优化策略：
	// 1. 使用 ST_Intersects 和 MBR 进行空间索引预过滤
	// 2. 利用 MySQL 空间索引的 R-tree 结构
	// 3. 减少需要精确计算距离的记录数量

	// 计算边界框（经纬度偏移量）
	latOffset := radius / 111000.0                                 // 转换为度
	lngOffset := radius / (111000.0 * math.Cos(lat*math.Pi/180.0)) // 考虑纬度影响

	// 使用 ST_Intersects 和 MBR 查询，最大化利用空间索引
	query := fmt.Sprintf(`SELECT %s, 
		ST_Distance_Sphere(
			ST_GeomFromText('POINT(%.6f %.6f)', 4326), 
			%s
		) as distance
		FROM %s
		WHERE ST_Intersects(
			%s,
			ST_GeomFromText('POLYGON((%.6f %.6f, %.6f %.6f, %.6f %.6f, %.6f %.6f, %.6f %.6f))', 4326)
		)
		AND ST_Distance_Sphere(
			ST_GeomFromText('POINT(%.6f %.6f)', 4326), 
			%s
		) <= ?
		ORDER BY distance
		LIMIT 100`,
		fieldsHotel,
		lng, lat, geoField, m.table,
		geoField,
		// 边界框坐标（矩形）
		lng-lngOffset, lat-latOffset, // 左下
		lng+lngOffset, lat-latOffset, // 右下
		lng+lngOffset, lat+latOffset, // 右上
		lng-lngOffset, lat+latOffset, // 左上
		lng-lngOffset, lat-latOffset, // 回到左下形成闭合多边形
		lng, lat, geoField)

	var results []*struct {
		Hotel
		Distance float64 `db:"distance"`
	}
	log.Infoc(ctx, "FindNearby high-performance query: radius=%.2f, lat=%.6f, lng=%.6f", radius, lat, lng)

	err := m.QueryRowsCtx(ctx, &results, query, radius)
	if err != nil {
		return nil, err
	}

	hotels := make([]*Hotel, len(results))
	for i, r := range results {
		hotels[i] = &r.Hotel
	}
	return hotels, nil
}

func (m *HotelModel) TransactUpdate(ctx context.Context, fn func(session sqlx.Session) error) error {
	return m.TransactCtx(ctx, func(ctx context.Context, s sqlx.Session) error {
		return fn(s)
	})
}
func (m *HotelModel) ValidateSRID(ctx context.Context, id int64) error {
	const checkSQL = `SELECT 
        ST_SRID(google_geo) as google_srid,
        ST_SRID(gaode_geo) as gaode_srid 
        FROM %s 
        WHERE id = ? 
        LIMIT 1`

	var result struct {
		GoogleSRID int `db:"google_srid"`
		GaodeSRID  int `db:"gaode_srid"`
	}

	err := m.QueryRowCtx(ctx, &result,
		fmt.Sprintf(checkSQL, m.table), id)

	if err != nil {
		return err
	}

	if result.GoogleSRID != geoDomain.SRID_WGS84 || result.GaodeSRID != geoDomain.SRID_WGS84 {
		return fmt.Errorf("SRID validation failed: google=%d, gaode=%d",
			result.GoogleSRID, result.GaodeSRID)
	}
	return nil
}

func (m *HotelModel) FindByIDs(ctx context.Context, ids []int64) ([]*Hotel, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	ids = utils.Deduplicate(ids)
	var resp []*Hotel
	// 使用 sqlx.In 处理数组参数
	baseQuery := fmt.Sprintf("SELECT %s FROM %s WHERE `id` IN (?)", fieldsHotel, m.table)

	// 展开查询参数
	query, args, err := jsqlx.In(baseQuery, ids)
	if err != nil {
		return nil, err
	}
	err = m.QueryRowsCtx(ctx, &resp, query, args...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlx.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func (m *HotelModel) Update(ctx context.Context, data *Hotel) error {
	// 注意：这里的字段列表需要根据实际需要更新，特别是 supplier SupplierHotelId 和 GeoPoint
	// 这里仅提供一个基础框架，假设更新除 SupplierHotelId, CreateTime 之外的所有字段
	// 实际应用中可能需要更精细的控制，比如只更新变化的字段

	// 构造 SET 子句，这里简化处理，实际应动态构建或使用 ORM 功能
	// 警告：直接拼接 SQL 可能有注入风险，且字段过多时难以维护
	// 更好的方式是使用 sqlx 的 NamedExec 或 go-zero 的 Update Builder (如果支持)
	// 或者手动构建 SET 子句和参数列表
	// 此处仅为示例，不建议在生产中使用如此简化的 Update
	updateSQL := fmt.Sprintf(`UPDATE %s SET
		city_region_id = ?, master_supplier = ?,
		supplier_1_id = ?, supplier_2_id = ?, supplier_3_id = ?, supplier_4_id = ?, supplier_5_id = ?,
		supplier_6_id = ?, supplier_7_id = ?, supplier_8_id = ?, supplier_9_id = ?, supplier_10_id = ?,
		supplier_11_id = ?, supplier_12_id = ?, supplier_13_id = ?, supplier_14_id = ?, supplier_15_id = ?,
		supplier_16_id = ?, supplier_17_id = ?, supplier_18_id = ?, supplier_19_id = ?, supplier_20_id = ?,
		supplier_21_id = ?, supplier_22_id = ?, supplier_23_id = ?, supplier_24_id = ?, supplier_25_id = ?,
		supplier_26_id = ?, supplier_27_id = ?, supplier_28_id = ?, supplier_29_id = ?, supplier_30_id = ?,
		google_geo = ST_GeomFromText(?, 4326), gaode_geo = ST_GeomFromText(?, 4326),
		min_price = ?, rating = ?, star = ?, brand_id = ?, static_profile = ?,
		is_deleted = ?
		WHERE id = ?`,
		m.table)

	googleGeoWKT := fmt.Sprintf("POINT(%.6f %.6f)", data.GoogleGeo.Point.Lat(), data.GoogleGeo.Point.Lon())
	gaodeGeoWKT := fmt.Sprintf("POINT(%.6f %.6f)", data.GaodeGeo.Point.Lat(), data.GaodeGeo.Point.Lon())

	_, err := m.ExecCtx(ctx, updateSQL,
		data.CityRegionId, data.MasterSupplier,
		data.Supplier1HotelId, data.Supplier2HotelId, data.Supplier3HotelId, data.Supplier4HotelId, data.Supplier5HotelId,
		data.Supplier6HotelId, data.Supplier7HotelId, data.Supplier8HotelId, data.Supplier9HotelId, data.Supplier10HotelId,
		data.Supplier11HotelId, data.Supplier12HotelId, data.Supplier13HotelId, data.Supplier14HotelId, data.Supplier15HotelId,
		data.Supplier16HotelId, data.Supplier17HotelId, data.Supplier18HotelId, data.Supplier19HotelId, data.Supplier20HotelId,
		data.Supplier21HotelId, data.Supplier22HotelId, data.Supplier23HotelId, data.Supplier24HotelId, data.Supplier25HotelId,
		data.Supplier26HotelId, data.Supplier27HotelId, data.Supplier28HotelId, data.Supplier29HotelId, data.Supplier30HotelId,
		googleGeoWKT, gaodeGeoWKT,
		data.MinPrice, data.Rating, data.Star, data.BrandId, data.StaticProfile,
		data.IsDeleted,
		data.Id) // WHERE 条件

	return err
}

// NewHotelModel returns a model for the database table.
func NewHotelModel(conn sqlx.SqlConn) *HotelModel {
	return &HotelModel{
		SqlConn: conn,
		table:   "hotel",
	}
}
