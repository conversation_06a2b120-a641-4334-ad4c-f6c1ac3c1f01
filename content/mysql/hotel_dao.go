package mysql

import (
	"context"
	"errors" // 新增 errors 导入
	"fmt"
	"reflect"
	"runtime"
	"time" // 新增 time 导入

	pkgerr "github.com/pkg/errors"

	"hotel/common/bizerr"
	"hotel/common/errgroup"
	"hotel/common/log"
	"hotel/common/pagehelper"
	"hotel/common/types"
	"hotel/content/domain"

	"github.com/bytedance/sonic"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx" // 新增 logx 导入
	"github.com/zeromicro/go-zero/core/stores/sqlc"
	"github.com/zeromicro/go-zero/core/stores/sqlx"

	geoDomain "hotel/geography/domain"
	supplierDomain "hotel/supplier/domain"
)

type HotelDao struct {
	h  *HotelModel
	hn *HotelNameModel
	hp *HotelEntityProfileModel
}

func NewHotelDao(conn sqlx.SqlConn) *HotelDao { // 返回接口类型
	return &HotelDao{
		h:  NewHotelModel(conn),
		hn: NewHotelNameModel(conn),
		hp: NewHotelEntityProfileModel(conn),
	}
}

func (d *HotelDao) Get(ctx context.Context, id types.ID) (*domain.Hotel, error) {
	h, err := d.h.FindByID(ctx, id.Int64())
	if err != nil {
		if errors.Is(err, ErrNotFound) { // 使用 ErrNotFound
			return nil, nil // 未找到返回
		}
		return nil, err
	}
	return convert2DomainHotel(h), nil
}

func (d *HotelDao) ListPage(ctx context.Context, regionIds types.IDs, pageReq pagehelper.PageReq, mandatorySuppliers []supplierDomain.Supplier) (domain.HotelList, *pagehelper.PageResp, error) {
	hs, pageResp, err := d.h.ListPage(ctx, regionIds, pageReq, mandatorySuppliers)
	if err != nil {
		return nil, nil, err
	}
	return convert2DomainHotels(hs), pageResp, nil
}

func (d *HotelDao) GetHotelEntityProfiles(ctx context.Context, entityID types.ID, hotelIds types.IDs) (map[types.ID]*domain.HotelEntityProfile, error) {
	vs, err := d.hp.FindByEntityIdAndHotelIds(ctx, entityID.Int64(), hotelIds.Int64s())
	if err != nil {
		return nil, err
	}
	out := make(map[types.ID]*domain.HotelEntityProfile)
	for _, v := range vs {
		out[types.ID(v.HotelId)] = convert2DomainHotelEntityProfile(v)
	}
	return out, nil
}
func convert2DomainHotelEntityProfile(h *HotelEntityProfile) *domain.HotelEntityProfile {
	return &domain.HotelEntityProfile{
		EntityHotelId:            h.EntityHotelId,
		HotelEntityProfileDetail: domain.HotelEntityProfileDetail{},
	}
}

// hard-rule-based hotel mapping implementation
func (d *HotelDao) Find(ctx context.Context, in *domain.Hotel) (domain.HotelList, error) {
	if in.ID > 0 {
		h, err := d.Get(ctx, in.ID)
		if err != nil {
			return nil, err
		}
		if h != nil {
			return []*domain.Hotel{h}, nil
		}
	}

	for _, ref := range in.HotelSupplierRef {
		if ref.Supplier == supplierDomain.Supplier_UNKNOWN {
			continue
		}
		h, err := d.h.FindBySupplierID(ctx, int(ref.Supplier), ref.SupplierHotelID)
		if err != nil && !bizerr.NotFoundErr.Is(err) {
			return nil, pkgerr.Wrapf(err, "FindBySupplierID failed %v:%v", ref.Supplier.String(), ref.SupplierHotelID)
		}
		if h != nil {
			log.Infoc(ctx, "find hotel by supplier %v:%v", ref.Supplier.String(), ref.SupplierHotelID)
			return []*domain.Hotel{convert2DomainHotel(h)}, err
		}
	}

	var (
		hotels domain.HotelList
		err    error
	)

	if !in.LatlngCoordinator.IsEmpty() {
		hotels, err = d.FindNearby(ctx, in.LatlngCoordinator, 500)
		if err != nil {
			return nil, pkgerr.Wrap(err, "FindNearby")
		}
		if len(hotels) == 1 {
			log.Infoc(ctx, "find hotel by latlng %v, got hotel: %v", in.LatlngCoordinator.Google, hotels[0].ID)
			return hotels, nil
		}
		if len(hotels) > 1 {
			log.Infoc(ctx, "find hotel by latlng %v, got hotels: %d, %v", in.LatlngCoordinator.Google, len(hotels), hotels.IDs())
		}
	}

	if !in.Name.IsEmpty() {
		// 使用 HotelNameModel 的 SearchName 进行模糊搜索
		// 假设默认返回 10 条记录
		var hotelNames []*HotelName
		if len(hotels) == 0 {
			hotelNames, err = d.hn.SearchName(ctx, in.Name, 10)
		} else {
			hotelNames, err = d.hn.SearchNameWithIds(ctx, in.Name, hotels.Int64IDs())
		}
		if err != nil {
			return nil, fmt.Errorf("failed to search hotel names: %w", err)
		}
		if len(hotelNames) == 0 {
			return nil, nil
		}

		log.Infoc(ctx, "find hotel by name %v, got hotels: %d, %#v", in.Name, len(hotelNames), hotelNames)
		// 根据 hotel_id 查询 Hotel 详情
		hotelIDs := make(types.IDs, 0, len(hotelNames))
		for _, hn := range hotelNames {
			hotelIDs = append(hotelIDs, types.ID(cast.ToInt64(hn.HotelId))) // 注意类型转换
		}

		hotels, err = d.FindByIDs(ctx, hotelIDs) // 假设 HotelModel 有 FindByIDs 方法
		if err != nil {
			return nil, fmt.Errorf("failed to find hotels by ids: %w", err)
		}
		return hotels, nil
	}

	return hotels, nil
}

func (d *HotelDao) FindNearby(ctx context.Context, latlng geoDomain.LatlngCoordinator, radius float64) (domain.HotelList, error) {
	st := time.Now()
	defer func() {
		log.Infoc(ctx, "FindNearby cost: %s, radius: %f, latlng: %v", time.Since(st), radius, latlng)
	}()
	res, err := d.h.FindNearby(ctx, latlng, radius)
	if err != nil {
		return nil, err
	}
	return convert2DomainHotels(res), nil
}

func (d *HotelDao) FindByIDs(ctx context.Context, ids types.IDs) (domain.HotelList, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	vs, err := d.h.FindByIDs(ctx, ids.Int64s())
	if err != nil {
		return nil, err
	}
	return convert2DomainHotels(vs), nil
}

// Insert 方法
func (d *HotelDao) Insert(ctx context.Context, in *domain.Hotel) error {
	modelHotel, err := convert2ModelHotel(in)
	if err != nil {
		return fmt.Errorf("failed to convert domain hotel to model: %w", err)
	}

	return d.h.Insert(ctx, modelHotel)
}

func (d *HotelDao) Update(ctx context.Context, in *domain.Hotel) error {
	if in.ID == 0 {
		return errors.New("hotel id is required for update")
	}
	modelHotel, err := convert2ModelHotel(in)
	if err != nil {
		return fmt.Errorf("failed to convert domain hotel to model: %w", err)
	}

	err = d.h.Update(ctx, modelHotel)
	if err != nil {
		if errors.Is(err, ErrNotFound) {
			return fmt.Errorf("hotel with SupplierHotelId %d not found for update", in.ID)
		}
		return fmt.Errorf("failed to update hotel: %w", err)

	}

	// update extra
	if !in.Name.IsEmpty() {
		if err = d.hn.UpsertName(ctx, in.ID.Int64(), in.Name); err != nil {
			return pkgerr.Wrap(err, "update hotel_name failed")
		}
	}
	return nil
}

func (d *HotelDao) Delete(ctx context.Context, ids types.IDs, hard bool) error {
	if len(ids) == 0 {
		return nil
	}
	var err error
	if hard {
		err = d.h.HardDelete(ctx, ids.Int64s())
	} else {
		err = d.h.SoftDelete(ctx, ids.Int64s())
	}
	if err != nil {
		if errors.Is(err, sqlc.ErrNotFound) || errors.Is(err, ErrNotFound) { // 检查两种可能的 Not Found 错误
			return fmt.Errorf("hotel with SupplierHotelId %+v not found for delete", ids)
		}
		return fmt.Errorf("failed to delete hotel: %w", err)
	}
	return nil
}

func (d *HotelDao) BatchInsert(ctx context.Context, in domain.HotelList) error {
	if len(in) == 0 {
		return nil
	}
	hs := make([]*Hotel, 0, len(in))
	now := time.Now()
	for _, h := range in {
		modelHotel, err := convert2ModelHotel(h)
		if err != nil {
			// 批量插入中单个转换失败，可以选择跳过或中断
			logx.Errorf("Failed to convert hotel SupplierHotelId %d for batch insert: %v", h.ID, err)
			continue // 跳过这个错误的记录
		}
		modelHotel.CreateTime = now
		modelHotel.UpdateTime = now
		modelHotel.IsDeleted = 0
		hs = append(hs, modelHotel)
	}
	if len(hs) == 0 {
		return errors.New("no valid hotels to insert after conversion")
	}
	if err := d.h.BatchInsert(ctx, hs); err != nil {
		return pkgerr.Wrapf(err, "failed to insert hotels(%+v)", in.IDs())
	}
	wg := errgroup.WithContext(ctx)
	wg.GOMAXPROCS(runtime.NumCPU())
	for _, h := range in {
		wg.Go(func(ctx context.Context) error {
			if err := d.hn.UpsertName(ctx, h.ID.Int64(), h.Name); err != nil {
				log.Errorc(ctx, "%v: failed to update hotel with SupplierHotelId %d, name(%#v)", err, h.ID, h.Name)
			}
			return nil
		})
	}
	return wg.Wait()
}

func convert2ModelHotel(in *domain.Hotel) (*Hotel, error) {
	out := &Hotel{
		Id:                    in.ID.Int64(),
		CityRegionId:          in.RegionId.Int64(),      // 假设 domain.Hotel 有 RegionId
		MasterSupplier:        int64(in.MasterSupplier), // 假设 domain.Hotel 有 MasterSupplier
		GoogleGeo:             geoDomain.ConvertLatLng2GeoPoint(in.HotelStaticProfile.LatlngCoordinator.Google),
		GaodeGeo:              geoDomain.ConvertLatLng2GeoPoint(in.HotelStaticProfile.LatlngCoordinator.Gaode), // 需要处理 nil 指针
		MinPrice:              in.MinPrice.Amount,                                                              // todo: 转成统一的搜索币种
		Rating:                in.HotelStaticProfile.Rating,                                                    // 假设 domain.Hotel 有 Rating
		Star:                  in.HotelStaticProfile.Star,                                                      // 假设 domain.Hotel 有 Star
		BrandId:               in.HotelStaticProfile.LoyaltyProgram.BrandId,                                    // 假设 domain.Hotel 有 BrandId
		LastRefreshStaticTime: in.HotelStaticProfile.LastRefreshStaticTime,
	}

	// 序列化 StaticProfile
	// 移除不必要的字段
	p := in.HotelStaticProfile
	p.Rating = 0
	p.Star = 0
	p.LoyaltyProgram.BrandId = 0
	p.LastRefreshStaticTime = time.Time{}
	staticProfileBytes, err := sonic.MarshalString(p)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal static profile: %w", err)
	}
	out.StaticProfile = staticProfileBytes

	// 填充 Supplier IDs
	for _, ref := range in.HotelSupplierRef {
		if err = out.SetSupplierID(ref.Supplier, ref.SupplierHotelID); err != nil {
			return nil, err
		}
	}
	return out, nil
}

func convert2DomainHotels(in []*Hotel) []*domain.Hotel {
	out := make([]*domain.Hotel, 0, len(in))
	for _, hotel := range in {
		out = append(out, convert2DomainHotel(hotel))
	}
	return out
}
func convert2DomainHotel(in *Hotel) *domain.Hotel {
	if in == nil {
		return nil
	}
	staticProfile := supplierDomain.HotelStaticProfile{}
	if in.StaticProfile != "" { // 检查是否为空，避免空字符串反序列化错误
		if err := sonic.UnmarshalString(in.StaticProfile, &staticProfile); err != nil {
			// 记录错误，但可能继续执行，返回部分数据
			logx.Errorf("Failed to unmarshal static profile for hotel SupplierHotelId %d: %v", in.Id, err)
			// 可以选择返回错误: return nil, fmt.Errorf(...)
		}
	}

	staticProfile.LatlngCoordinator.Google = geoDomain.ConvertGeoPoint2LatLngPointer(in.GoogleGeo)
	staticProfile.LatlngCoordinator.Gaode = geoDomain.ConvertGeoPoint2LatLngPointer(in.GaodeGeo)
	staticProfile.LoyaltyProgram.BrandId = in.BrandId
	staticProfile.Rating = in.Rating
	staticProfile.Star = in.Star
	staticProfile.LastRefreshStaticTime = in.LastRefreshStaticTime
	staticProfile.RegionId = types.ID(in.CityRegionId) // 假设 domain.Hotel 有 RegionId

	out := &domain.Hotel{
		ID:                 types.ID(in.Id),
		MasterSupplier:     supplierDomain.Supplier(in.MasterSupplier), // 假设 domain.Hotel 有 MasterSupplier
		HotelStaticProfile: staticProfile,
		HotelSupplierRef: func() (out []supplierDomain.HotelSupplierRef) {
			// 使用反射获取 Supplier SupplierHotelId
			val := reflect.ValueOf(in).Elem()
			for supplierNum, fieldIndex := range supplierFields {
				supHotelID := val.Field(fieldIndex).String()
				if supHotelID != "" {
					out = append(out, supplierDomain.HotelSupplierRef{
						Supplier:        supplierDomain.Supplier(supplierNum),
						SupplierHotelID: supHotelID,
					})
				}
			}
			return out
		}(),
	}

	return out
}

// 在文件顶部添加缓存变量
var (
	supplierFields map[supplierDomain.Supplier]int // 缓存字段索引，key: 供应商编号，value: 字段索引
)

// 在init函数中初始化字段缓存
func init() {
	supplierFields = make(map[supplierDomain.Supplier]int)
	t := reflect.TypeOf(Hotel{})
	for i := coreSupplierMin; i <= coreSupplierMax; i++ {
		fieldName := fmt.Sprintf("Supplier%dHotelId", i)
		if field, ok := t.FieldByName(fieldName); ok {
			supplierFields[supplierDomain.Supplier(i)] = field.Index[0]
		}
	}
}

// GetSupplierID 方法移到 Hotel 结构体外或作为辅助函数，因为它操作的是 model.Hotel
// 或者保持原样，但在 convert2DomainHotel 中使用反射是更通用的方式
func (h *Hotel) GetSupplierID(num supplierDomain.Supplier) (string, error) {
	val := reflect.ValueOf(h).Elem()
	return val.Field(supplierFields[num]).String(), nil
}

func (h *Hotel) SetSupplierID(num supplierDomain.Supplier, id string) error {
	val := reflect.ValueOf(h).Elem()
	field := val.Field(supplierFields[num])
	if field.IsValid() && field.CanSet() {
		field.SetString(id)
	}
	return nil
}

const (
	coreSupplierMax = 30
	coreSupplierMin = 1
)
