package mysql

import (
	"testing"

	supplierDomain "hotel/supplier/domain"

	"github.com/stretchr/testify/assert"
)

// TestSupplierExtension tests the extended supplier support from 20 to 30 suppliers
func TestSupplierExtension(t *testing.T) {
	// Test that new supplier constants are defined correctly
	t.Run("TestNewSupplierConstants", func(t *testing.T) {
		// Test that new suppliers have valid IDs
		assert.Equal(t, 23, int(supplierDomain.Supplier_Reserved23))
		assert.Equal(t, 24, int(supplierDomain.Supplier_Reserved24))
		assert.Equal(t, 25, int(supplierDomain.Supplier_Reserved25))
		assert.Equal(t, 26, int(supplierDomain.Supplier_Reserved26))
		assert.Equal(t, 27, int(supplierDomain.Supplier_Reserved27))
		assert.Equal(t, 28, int(supplierDomain.Supplier_Reserved28))
		assert.Equal(t, 29, int(supplierDomain.Supplier_Reserved29))
		assert.Equal(t, 30, int(supplierDomain.Supplier_Reserved30))
	})

	t.Run("TestSupplierNames", func(t *testing.T) {
		// Test English names
		assert.Equal(t, "Reserved23", supplierDomain.Supplier_Reserved23.GetEnglishName())
		assert.Equal(t, "Reserved24", supplierDomain.Supplier_Reserved24.GetEnglishName())
		assert.Equal(t, "Reserved30", supplierDomain.Supplier_Reserved30.GetEnglishName())

		// Test Chinese names
		assert.Equal(t, "预留23", supplierDomain.Supplier_Reserved23.GetChineseName())
		assert.Equal(t, "预留24", supplierDomain.Supplier_Reserved24.GetChineseName())
		assert.Equal(t, "预留30", supplierDomain.Supplier_Reserved30.GetChineseName())
	})

	t.Run("TestGetAllSuppliers", func(t *testing.T) {
		suppliers := supplierDomain.GetAllSuppliers()

		// Should have at least 30 suppliers (including reserved ones)
		assert.GreaterOrEqual(t, len(suppliers), 30)

		// Check that new suppliers are included
		found23 := false
		found30 := false
		for _, supplier := range suppliers {
			if supplier.ID == 23 {
				found23 = true
				assert.Equal(t, "Reserved23", supplier.Name)
				assert.Equal(t, "预留23", supplier.ChineseName)
				assert.False(t, supplier.IsActive) // Reserved suppliers should be inactive
			}
			if supplier.ID == 30 {
				found30 = true
				assert.Equal(t, "Reserved30", supplier.Name)
				assert.Equal(t, "预留30", supplier.ChineseName)
				assert.False(t, supplier.IsActive)
			}
		}
		assert.True(t, found23, "Supplier_Reserved23 should be in GetAllSuppliers()")
		assert.True(t, found30, "Supplier_Reserved30 should be in GetAllSuppliers()")
	})

	t.Run("TestHotelModelFields", func(t *testing.T) {
		// Test that Hotel struct has all the new fields
		hotel := &Hotel{}

		// Test setting supplier IDs for new suppliers
		err := hotel.SetSupplierID(supplierDomain.Supplier_Reserved23, "test_hotel_23")
		assert.NoError(t, err)

		err = hotel.SetSupplierID(supplierDomain.Supplier_Reserved30, "test_hotel_30")
		assert.NoError(t, err)

		// Test getting supplier IDs
		id23, err := hotel.GetSupplierID(supplierDomain.Supplier_Reserved23)
		assert.NoError(t, err)
		assert.Equal(t, "test_hotel_23", id23)

		id30, err := hotel.GetSupplierID(supplierDomain.Supplier_Reserved30)
		assert.NoError(t, err)
		assert.Equal(t, "test_hotel_30", id30)
	})

	t.Run("TestCoreSupplierMaxConstant", func(t *testing.T) {
		// Test that coreSupplierMax has been updated to 30
		// This is tested indirectly by checking if SetSupplierID works for supplier 30
		hotel := &Hotel{}
		err := hotel.SetSupplierID(supplierDomain.Supplier_Reserved30, "test_30")
		assert.NoError(t, err, "Should be able to set supplier ID for supplier 30")

		id, err := hotel.GetSupplierID(supplierDomain.Supplier_Reserved30)
		assert.NoError(t, err)
		assert.Equal(t, "test_30", id)
	})
}
