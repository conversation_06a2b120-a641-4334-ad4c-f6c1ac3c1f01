package protocol

import (
	"hotel/common/pagehelper"
	"hotel/common/types"
	"hotel/content/domain"
	supplierDomain "hotel/supplier/domain"
	userDomain "hotel/user/domain"
)

// OperateHotelReq represents the request structure for hotel operation with 2 fields
type OperateHotelReq struct {
	Hotel    *domain.Hotel    `json:"hotel"`               // Hotel contains the hotel data for this operation
	Operator *userDomain.User `json:"operator" apidoc:"-"` // Operator contains the user data for this operation
}

// OperateHotelResp represents the response structure for hotel operation with 1 fields
type OperateHotelResp struct {
	*domain.Hotel // Hotel contains the hotel data for this operation
}

// GetHotelReq represents the request structure for hotel retrieval operation with 1 fields
type GetHotelReq struct {
	HotelId types.ID `json:"hotelId"` // HotelId is the unique identifier for this hotel
}

// GetHotelResp represents the response structure for hotel retrieval operation with 1 fields
type GetHotelResp struct {
	Hotel *domain.Hotel `json:"hotel,omitempty"` // Hotel contains the hotel data for this retrieval
}

// MGetHotelReq represents the request structure for multiple hotel retrieval operation with 1 fields
type MGetHotelReq struct {
	HotelIds types.IDs `json:"hotelIds"` // HotelIds contains a list of hotel identifier items
}

// MGetHotelResp represents the response structure for multiple hotel retrieval operation with 1 fields
type MGetHotelResp struct {
	Hotels domain.HotelList `json:"hotels"` // Hotels contains the hotel list data for this retrieval
}

// ListHotelByRegionIdsReq represents the request structure for hotel listing by region operation with 4 fields
type ListHotelByRegionIdsReq struct {
	Operator           *userDomain.User          `json:"operator" apidoc:"-"`                            // Operator contains the user data for this listing
	RegionIds          types.IDs                 `json:"regionIds"`                                      // RegionIds contains a list of region identifier items
	InternalSuppliers  []supplierDomain.Supplier `json:"internalSuppliers,omitempty" apidoc:"HotelCode"` // InternalSuppliers contains a list of supplier items
	pagehelper.PageReq                           // PageReq contains the pagination request data
}

// ListHotelByRegionIdsResp represents the response structure for hotel listing by region operation with 2 fields
type ListHotelByRegionIdsResp struct {
	Hotels               domain.HotelList `json:"hotels"` // Hotels contains the hotel list data for this listing
	*pagehelper.PageResp                  // PageResp contains the pagination response data
}

// HotelIDListReq represents the request structure for hotel ID listing operation with 6 fields
type HotelIDListReq struct {
	Operator         *userDomain.User        `json:"operator" apidoc:"-"`       // Operator contains the user data for this listing
	RegionId         types.ID                `json:"regionId,omitzero"`         // RegionId is the unique identifier for this region
	HotelIds         types.IDs               `json:"hotelIds,omitzero"`         // HotelIds contains a list of hotel identifier items
	BusinessHotelIds []string                `json:"businessHotelIds,omitzero"` // BusinessHotelIds contains a list of business hotel identifier items
	Supplier         supplierDomain.Supplier `json:"supplier"`                  // Supplier contains the supplier data for this listing
	Page             pagehelper.PageReq      `json:"page,omitzero"`             // Page contains the pagination request data
}

// HotelIDListResp represents the response structure for hotel ID listing operation with 2 fields
type HotelIDListResp struct {
	HotelIdList []HotelId            `json:"hotelIdList"`   // HotelIdList contains a list of hotel ID items
	Page        *pagehelper.PageResp `json:"page,omitzero"` // Page contains the pagination response data
}

// HotelId represents a data structure for API communication with 3 fields
type HotelId struct {
	HotelId                         types.ID `json:"hotelId,omitempty"`         // HotelId is the unique identifier for this hotel
	BusinessHotelId                 string   `json:"businessHotelId,omitempty"` // BusinessHotelId contains the business hotel identifier value
	supplierDomain.HotelSupplierRef          // HotelSupplierRef contains the hotel supplier reference data
}
