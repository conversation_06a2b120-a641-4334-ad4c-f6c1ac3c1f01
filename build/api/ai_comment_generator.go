package main

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"os"
	"path/filepath"
	"strings"
)

// AICommentGenerator AI注释生成器
type AICommentGenerator struct {
	processedFiles map[string]bool
}

// NewAICommentGenerator 创建新的AI注释生成器
func NewAICommentGenerator() *AICommentGenerator {
	return &AICommentGenerator{
		processedFiles: make(map[string]bool),
	}
}

// GenerateCommentsForProtocolFiles 为协议文件生成AI注释
func (g *AICommentGenerator) GenerateCommentsForProtocolFiles() error {
	// 查找所有protocol目录
	protocolDirs := []string{
		"api/protocol",
		"user/protocol",
		"trade/protocol",
		"search/protocol",
		"content/protocol",
		"geography/protocol",
		"rule/protocol",
		"bi/protocol",
		"notify/protocol",
	}

	for _, dir := range protocolDirs {
		if err := g.processDirectory(dir); err != nil {
			return fmt.Errorf("处理目录 %s 失败: %w", dir, err)
		}
	}

	return nil
}

// processDirectory 处理指定目录下的所有Go文件
func (g *AICommentGenerator) processDirectory(dir string) error {
	return filepath.Walk(dir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		if info.IsDir() {
			return nil
		}

		if !strings.HasSuffix(path, ".go") {
			return nil
		}

		return g.processFile(path)
	})
}

// processFile 处理单个Go文件
func (g *AICommentGenerator) processFile(filePath string) error {
	if g.processedFiles[filePath] {
		return nil
	}

	// 读取文件内容
	content, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("读取文件 %s 失败: %w", filePath, err)
	}

	// 解析Go文件
	fset := token.NewFileSet()
	file, err := parser.ParseFile(fset, filePath, content, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析文件 %s 失败: %w", filePath, err)
	}

	// 生成AI注释
	modified := g.addAIComments(file, string(content))

	if modified != string(content) {
		// 写回文件
		if err := os.WriteFile(filePath, []byte(modified), 0644); err != nil {
			return fmt.Errorf("写入文件 %s 失败: %w", filePath, err)
		}
		fmt.Printf("已为文件 %s 添加AI注释\n", filePath)
	}

	g.processedFiles[filePath] = true
	return nil
}

// addAIComments 为AST节点添加AI注释
func (g *AICommentGenerator) addAIComments(file *ast.File, originalContent string) string {
	var modifications []modification

	// 处理所有类型声明
	for _, decl := range file.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok {
			for _, spec := range genDecl.Specs {
				if typeSpec, ok := spec.(*ast.TypeSpec); ok {
					if structType, ok := typeSpec.Type.(*ast.StructType); ok {
						mods := g.addStructAIComments(typeSpec, structType, originalContent)
						modifications = append(modifications, mods...)
					}
				}
			}
		}
	}

	// 应用修改
	return g.applyModifications(originalContent, modifications)
}

// addStructAIComments 为结构体添加AI注释
func (g *AICommentGenerator) addStructAIComments(typeSpec *ast.TypeSpec, structType *ast.StructType, content string) []modification {
	var modifications []modification

	// 检查是否已经有AI注释
	if g.hasAIComment(typeSpec.Doc) {
		return modifications
	}

	// 为结构体添加AI注释
	structComment := g.generateStructAIComment(typeSpec.Name.Name, structType)
	if structComment != "" {
		mod := modification{
			start: typeSpec.Pos() - 1,
			end:   typeSpec.Pos() - 1,
			text:  structComment + "\n",
		}
		modifications = append(modifications, mod)
	}

	// 为字段添加AI注释
	for _, field := range structType.Fields.List {
		if len(field.Names) > 0 && field.Doc == nil {
			fieldComment := g.generateFieldAIComment(field.Names[0].Name, field.Type)
			if fieldComment != "" {
				mod := modification{
					start: field.Pos() - 1,
					end:   field.Pos() - 1,
					text:  "\t" + fieldComment + "\n",
				}
				modifications = append(modifications, mod)
			}
		}
	}

	return modifications
}

// generateStructAIComment 生成结构体的AI注释
func (g *AICommentGenerator) generateStructAIComment(structName string, structType *ast.StructType) string {
	// 根据结构体名称和字段生成AI注释
	var comment string

	switch {
	case strings.HasSuffix(structName, "Req"):
		comment = fmt.Sprintf("// %s represents the request structure for %s operation", structName, strings.TrimSuffix(structName, "Req"))
	case strings.HasSuffix(structName, "Resp"):
		comment = fmt.Sprintf("// %s represents the response structure for %s operation", structName, strings.TrimSuffix(structName, "Resp"))
	case strings.HasSuffix(structName, "Request"):
		comment = fmt.Sprintf("// %s represents the request structure for %s operation", structName, strings.TrimSuffix(structName, "Request"))
	case strings.HasSuffix(structName, "Response"):
		comment = fmt.Sprintf("// %s represents the response structure for %s operation", structName, strings.TrimSuffix(structName, "Response"))
	default:
		comment = fmt.Sprintf("// %s represents a data structure for API communication", structName)
	}

	// 添加字段数量信息
	fieldCount := len(structType.Fields.List)
	if fieldCount > 0 {
		comment += fmt.Sprintf(" with %d fields", fieldCount)
	}

	return comment
}

// generateFieldAIComment 生成字段的AI注释
func (g *AICommentGenerator) generateFieldAIComment(fieldName string, fieldType ast.Expr) string {
	// 根据字段名称和类型生成AI注释
	var comment string

	// 分析字段类型
	typeStr := g.getTypeString(fieldType)

	switch {
	case strings.Contains(strings.ToLower(fieldName), "id"):
		comment = fmt.Sprintf("// %s is the unique identifier for this %s", fieldName, strings.TrimSuffix(fieldName, "Id"))
	case strings.Contains(strings.ToLower(fieldName), "name"):
		comment = fmt.Sprintf("// %s is the display name for this %s", fieldName, strings.TrimSuffix(fieldName, "Name"))
	case strings.Contains(strings.ToLower(fieldName), "time"):
		comment = fmt.Sprintf("// %s represents the timestamp when this %s occurred", fieldName, strings.TrimSuffix(fieldName, "Time"))
	case strings.Contains(strings.ToLower(fieldName), "date"):
		comment = fmt.Sprintf("// %s represents the date for this %s", fieldName, strings.TrimSuffix(fieldName, "Date"))
	case strings.Contains(strings.ToLower(fieldName), "email"):
		comment = fmt.Sprintf("// %s is the email address for this %s", fieldName, strings.TrimSuffix(fieldName, "Email"))
	case strings.Contains(strings.ToLower(fieldName), "phone"):
		comment = fmt.Sprintf("// %s is the phone number for this %s", fieldName, strings.TrimSuffix(fieldName, "Phone"))
	case strings.Contains(strings.ToLower(fieldName), "url"):
		comment = fmt.Sprintf("// %s is the URL for this %s", fieldName, strings.TrimSuffix(fieldName, "Url"))
	case strings.Contains(strings.ToLower(fieldName), "code"):
		comment = fmt.Sprintf("// %s is the code for this %s", fieldName, strings.TrimSuffix(fieldName, "Code"))
	case strings.Contains(strings.ToLower(fieldName), "status"):
		comment = fmt.Sprintf("// %s represents the current status of this %s", fieldName, strings.TrimSuffix(fieldName, "Status"))
	case strings.Contains(strings.ToLower(fieldName), "amount"):
		comment = fmt.Sprintf("// %s represents the monetary amount for this %s", fieldName, strings.TrimSuffix(fieldName, "Amount"))
	case strings.Contains(strings.ToLower(fieldName), "price"):
		comment = fmt.Sprintf("// %s represents the price for this %s", fieldName, strings.TrimSuffix(fieldName, "Price"))
	case strings.Contains(strings.ToLower(fieldName), "currency"):
		comment = fmt.Sprintf("// %s represents the currency code for this %s", fieldName, strings.TrimSuffix(fieldName, "Currency"))
	case strings.Contains(strings.ToLower(fieldName), "list"):
		comment = fmt.Sprintf("// %s contains a list of %s items", fieldName, strings.TrimSuffix(fieldName, "List"))
	case strings.Contains(strings.ToLower(fieldName), "count"):
		comment = fmt.Sprintf("// %s represents the count of %s", fieldName, strings.TrimSuffix(fieldName, "Count"))
	case strings.Contains(strings.ToLower(fieldName), "total"):
		comment = fmt.Sprintf("// %s represents the total number of %s", fieldName, strings.TrimSuffix(fieldName, "Total"))
	case strings.Contains(strings.ToLower(fieldName), "message"):
		comment = fmt.Sprintf("// %s contains the message for this %s", fieldName, strings.TrimSuffix(fieldName, "Message"))
	case strings.Contains(strings.ToLower(fieldName), "error"):
		comment = fmt.Sprintf("// %s contains error information for this %s", fieldName, strings.TrimSuffix(fieldName, "Error"))
	case strings.Contains(strings.ToLower(fieldName), "success"):
		comment = fmt.Sprintf("// %s indicates whether this %s was successful", fieldName, strings.TrimSuffix(fieldName, "Success"))
	case strings.Contains(strings.ToLower(fieldName), "data"):
		comment = fmt.Sprintf("// %s contains the main data for this %s", fieldName, strings.TrimSuffix(fieldName, "Data"))
	case strings.Contains(strings.ToLower(fieldName), "info"):
		comment = fmt.Sprintf("// %s contains information about this %s", fieldName, strings.TrimSuffix(fieldName, "Info"))
	case strings.Contains(strings.ToLower(fieldName), "detail"):
		comment = fmt.Sprintf("// %s contains detailed information about this %s", fieldName, strings.TrimSuffix(fieldName, "Detail"))
	case strings.Contains(strings.ToLower(fieldName), "config"):
		comment = fmt.Sprintf("// %s contains configuration for this %s", fieldName, strings.TrimSuffix(fieldName, "Config"))
	case strings.Contains(strings.ToLower(fieldName), "param"):
		comment = fmt.Sprintf("// %s contains parameters for this %s", fieldName, strings.TrimSuffix(fieldName, "Param"))
	case strings.Contains(strings.ToLower(fieldName), "option"):
		comment = fmt.Sprintf("// %s contains options for this %s", fieldName, strings.TrimSuffix(fieldName, "Option"))
	default:
		// 根据类型生成通用注释
		switch typeStr {
		case "string":
			comment = fmt.Sprintf("// %s contains the %s value", fieldName, strings.ToLower(fieldName))
		case "int", "int64", "int32":
			comment = fmt.Sprintf("// %s represents the %s as an integer", fieldName, strings.ToLower(fieldName))
		case "float64", "float32":
			comment = fmt.Sprintf("// %s represents the %s as a decimal number", fieldName, strings.ToLower(fieldName))
		case "bool":
			comment = fmt.Sprintf("// %s indicates whether %s is enabled", fieldName, strings.ToLower(fieldName))
		case "time.Time":
			comment = fmt.Sprintf("// %s represents the timestamp for %s", fieldName, strings.ToLower(fieldName))
		default:
			comment = fmt.Sprintf("// %s contains the %s data", fieldName, strings.ToLower(fieldName))
		}
	}

	return comment
}

// getTypeString 获取类型的字符串表示
func (g *AICommentGenerator) getTypeString(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.Ident:
		return t.Name
	case *ast.StarExpr:
		return "*" + g.getTypeString(t.X)
	case *ast.ArrayType:
		return "[]" + g.getTypeString(t.Elt)
	case *ast.SelectorExpr:
		return g.getTypeString(t.X) + "." + t.Sel.Name
	case *ast.MapType:
		return "map[" + g.getTypeString(t.Key) + "]" + g.getTypeString(t.Value)
	case *ast.InterfaceType:
		return "interface{}"
	default:
		return "unknown"
	}
}

// hasAIComment 检查是否已经有AI注释
func (g *AICommentGenerator) hasAIComment(doc *ast.CommentGroup) bool {
	if doc == nil {
		return false
	}

	for _, comment := range doc.List {
		if strings.Contains(comment.Text, "//apidoc:zh") {
			return true
		}
	}

	return false
}

// modification 表示对文件的修改
type modification struct {
	start token.Pos
	end   token.Pos
	text  string
}

// applyModifications 应用修改到文件内容
func (g *AICommentGenerator) applyModifications(content string, modifications []modification) string {
	if len(modifications) == 0 {
		return content
	}

	// 按位置排序修改
	for i := 0; i < len(modifications)-1; i++ {
		for j := i + 1; j < len(modifications); j++ {
			if modifications[i].start > modifications[j].start {
				modifications[i], modifications[j] = modifications[j], modifications[i]
			}
		}
	}

	// 应用修改
	result := content
	offset := 0

	for _, mod := range modifications {
		start := int(mod.start) + offset
		end := int(mod.end) + offset

		if start >= 0 && start <= len(result) && end >= start && end <= len(result) {
			result = result[:start] + mod.text + result[end:]
			offset += len(mod.text) - (end - start)
		}
	}

	return result
}
