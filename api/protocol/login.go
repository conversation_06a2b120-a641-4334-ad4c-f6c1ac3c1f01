package protocol

import (
	"hotel/user/domain"
)

// LoginReq represents the request structure for user authentication operation with 3 fields
type LoginReq struct {
	Email    string `json:"email" required:"true"`    // Email is the email address for this authentication
	Password string `json:"password" required:"true"` // Password is the password for this authentication
	TTL      int64  `json:"ttl"`                      // TTL represents the time-to-live as an integer for this authentication
}

// LoginResp represents the response structure for user authentication operation with 2 fields
type LoginResp struct {
	Token string       `json:"token"` // Token contains the authentication token value
	User  *domain.User `json:"user"`  // User contains the user data for this authentication
}

// TicketReq represents the request structure for ticket generation operation with 3 fields
type TicketReq struct {
	AppKey    string `json:"appKey" required:"true"`    // AppKey is the application key for this ticket
	AppSecret string `json:"appSecret" required:"true"` // AppSecret is the application secret for this ticket
	TTL       int64  `json:"ttl"`                       // TTL represents the time-to-live as an integer for this ticket
}

// TicketResp represents the response structure for ticket generation operation with 1 fields
type TicketResp struct {
	Ticket string `json:"ticket"` // Ticket contains the generated ticket value
}

// ForgetPasswordReq represents the request structure for password reset operation with 1 fields
type ForgetPasswordReq struct {
	Email string `json:"email"` // Email is the email address for this password reset
}

// ForgetPasswordResp represents the response structure for password reset operation with 0 fields
type ForgetPasswordResp struct {
}

// ResetPasswordReq represents the request structure for password change operation with 3 fields
type ResetPasswordReq struct {
	Operator    *domain.User `json:"operator" apidoc:"-"` // Operator contains the user data for this password change
	NewPassword string       `json:"newPassword"`         // NewPassword contains the new password value
	OldPassword string       `json:"oldPassword"`         // OldPassword contains the old password value
}

// ResetPasswordResp represents the response structure for password change operation with 0 fields
type ResetPasswordResp struct {
}
