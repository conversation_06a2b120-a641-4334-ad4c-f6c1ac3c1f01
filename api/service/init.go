package service

import (
	"fmt"
	"hotel/common/quota"

	"hotel/api/config"
	biService "hotel/bi/service"
	httpUtil "hotel/common/httpdispatcher"
	contentService "hotel/content/service"
	geoService "hotel/geography/service"
	ruleService "hotel/rule/service"
	searchService "hotel/search/service"
	"hotel/supplier"
	tradeService "hotel/trade/service"
	userService "hotel/user/service"
)

type Dependency struct {
	Config           config.Config
	UserSrv          *userService.UserService
	RuleSrv          *ruleService.RuleService
	TradeSrv         *tradeService.TradeService
	GeoSrv           *geoService.GeographyService
	SearchSrv        *searchService.SearchService
	ContentSrv       *contentService.ContentService
	BISrv            *biService.OrderService
	Supplier         *supplier.Factory
	ViewSrv          *ViewService
	AuthSrv          *AuthService
	FunSrv           *FunService
	StarlingSrv      *contentService.StarlingService
	QuestionnaireSrv *QuestionnaireService
	LogSrv           *biService.LogService
	JwtAuth          *httpUtil.JwtPlugin // 使用自定义的 JWT 管理器
}

func NewServiceContext(c config.Config) *Dependency {
	jwtAuth, err := httpUtil.NewJWTPlugin(c.JwtAuth)
	if err != nil {
		panic(fmt.Errorf("httpUtil.NewJWTPlugin:%w", err))
	}

	userSrv := userService.NewUserService()
	jwtAuth.WithUpdater(userSrv.JwtUpdater)
	authSrv := NewAuthService(jwtAuth, userSrv)
	ruleSrv := ruleService.NewRuleService()
	tradeSrv := tradeService.NewTradeService()
	geoSrv := geoService.NewGeographyService()
	contentSrv := contentService.NewContentService()
	searchSrv := searchService.NewSearchService()
	starlingSrv := contentService.NewStarlingService()
	questionnaireSrv := NewQuestionnaireService(c)
	logSrv := biService.NewLogService()
	biSrv := biService.NewOrderService()
	err = quota.InitWithDatasource(userSrv.GetQuotaDatasource())
	if err != nil {
		panic(fmt.Errorf("quota.InitWithDatasource:%w", err))
	}
	return &Dependency{
		Config:           c,
		Supplier:         supplier.NewFactory(),
		UserSrv:          userSrv,
		RuleSrv:          ruleSrv,
		JwtAuth:          jwtAuth,
		TradeSrv:         tradeSrv,
		AuthSrv:          authSrv,
		GeoSrv:           geoSrv,
		SearchSrv:        searchSrv,
		ContentSrv:       contentSrv,
		BISrv:            biSrv,
		ViewSrv:          NewViewService(),
		FunSrv:           NewFunService(),
		StarlingSrv:      starlingSrv,
		QuestionnaireSrv: questionnaireSrv,
		LogSrv:           logSrv,
	}
}
