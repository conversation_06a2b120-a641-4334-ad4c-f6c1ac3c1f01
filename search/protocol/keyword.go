package protocol

import (
	"hotel/content/domain"
	geoProto "hotel/geography/protocol"
)

// SearchReq represents the request structure for keyword search operation with 2 fields
type SearchReq struct {
	Keyword string        `json:"keyword"`          // Keyword contains the search keyword value
	Context SearchContext `json:"context,omitzero"` // Context contains the search context data
}

// SearchContext represents a data structure for API communication with 1 fields
type SearchContext struct {
	RegionId string `json:"regionId"` // RegionId is the unique identifier for this region
}

// SearchResp represents the response structure for keyword search operation with 1 fields
type SearchResp struct {
	Candidates []*SearchItem `json:"candidates"` // Candidates contains a list of search result items
}

// SearchItem represents a data structure for API communication with 2 fields
type SearchItem struct {
	geoProto.FuzzySearchItem               // FuzzySearchItem contains the fuzzy search result data
	Hotel                    *domain.Hotel `json:"hotel"` // Hotel contains the hotel data for this search result
}
