package service

import (
	"context"
	"fmt"
	"hotel/common/bff"
	"hotel/common/i18n"
	"hotel/common/pagehelper"
	"hotel/common/utils"
	"hotel/user/domain"

	"hotel/user/protocol"
)

// AddAuditLog
// @desc: 新增审计日志
// @apidoc: -
func (s *UserService) AddAuditLog(ctx context.Context, req *protocol.AddAuditLogReq) error {
	return s.dao.AuditLog.Insert(ctx, req.Log)
}

// ListAuditLogs represents the enhanced audit log listing service with advanced filtering and statistics
func (s *UserService) ListAuditLogs(ctx context.Context, req *protocol.ListAuditLogReq) (*protocol.ListAuditLogResp, error) {
	// Build query parameters
	query := &domain.AuditLogQuery{
		ActionType:     req.ActionType,
		Keyword:        req.Keyword,
		IncludeDetails: req.IncludeDetails,
		SortBy:         req.SortBy,
		SortOrder:      req.SortOrder,
		Offset:         int(req.PageReq.GetOffset()),
		Limit:          int(req.PageReq.PageSize),
	}

	// Set time range if provided
	if !req.ActionTime.Start.IsZero() {
		query.ActionTimeStart = &req.ActionTime.Start
	}
	if !req.ActionTime.End.IsZero() {
		query.ActionTimeEnd = &req.ActionTime.End
	}

	// Set user and entity filters if provided
	if req.ActorUserId != nil {
		query.ActorUserId = req.ActorUserId
	}
	if req.AffectedEntityId != nil {
		query.AffectedEntityId = req.AffectedEntityId
	}

	// Query audit logs
	logs, err := s.dao.AuditLog.QueryAuditLogs(ctx, query)
	if err != nil {
		return nil, err
	}

	// Get total count
	totalCount, err := s.dao.AuditLog.CountAuditLogs(ctx, query)
	if err != nil {
		return nil, err
	}

	// Get statistics if requested
	var statistics *protocol.AuditLogStatistics
	if req.IncludeDetails {
		stats, err := s.dao.AuditLog.GetAuditLogStatistics(ctx, query)
		if err == nil {
			statistics = convert2ProtocolAuditLogStatistics(stats)
		}
	}

	// Convert domain logs to BFF ElementRow format
	var elementRows []*bff.ElementRow[domain.AuditLog]
	for _, log := range logs {
		elementRows = append(elementRows, &bff.ElementRow[domain.AuditLog]{
			Raw: *log,
			Key: fmt.Sprintf("audit_%d", log.Id),
			Columns: []bff.ElementItem{
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: log.ActionType, Zh: log.ActionType, Ar: log.ActionType},
					},
				},
				{
					Type: bff.ElementTypeTime,
					Content: &bff.ElementTime{
						Time: log.ActionTime,
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: log.ActorUserId.String(), Zh: log.ActorUserId.String(), Ar: log.ActorUserId.String()},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: log.ActorInfo, Zh: log.ActorInfo, Ar: log.ActorInfo},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: log.AffectedEntityId.String(), Zh: log.AffectedEntityId.String(), Ar: log.AffectedEntityId.String()},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: log.AffectedEntityInfo, Zh: log.AffectedEntityInfo, Ar: log.AffectedEntityInfo},
					},
				},
				{
					Type: bff.ElementTypeText,
					Content: bff.TextContent{
						Text: i18n.I18N{En: utils.ToLogString(log.Details), Zh: utils.ToLogString(log.Details), Ar: utils.ToLogString(log.Details)},
					},
				},
			},
		})
	}

	return &protocol.ListAuditLogResp{
		Table: bff.Table[domain.AuditLog]{
			PageResp: pagehelper.PageResp{
				Total:   totalCount,
				HasMore: len(logs) >= int(req.PageReq.PageSize),
			},
			HeaderKeys: []string{
				"actionType",
				"actionTime",
				"actorUserId",
				"actorInfo",
				"affectedEntityId",
				"affectedEntityInfo",
				"details",
			},
			Rows:                 elementRows,
			Header:               nil,
			StyleId:              "",
			IsRowToColumnEnabled: false,
		},
		Statistics: statistics,
	}, nil
}

// convert2ProtocolAuditLogStatistics converts domain statistics to protocol statistics
func convert2ProtocolAuditLogStatistics(stats *domain.AuditLogStatistics) *protocol.AuditLogStatistics {
	if stats == nil {
		return nil
	}

	protocolStats := &protocol.AuditLogStatistics{
		TotalCount:       stats.TotalCount,
		ActionTypeCount:  stats.ActionTypeCount,
		TopActors:        make([]*protocol.ActorStatistics, len(stats.TopActors)),
		TopEntities:      make([]*protocol.EntityStatistics, len(stats.TopEntities)),
		TimeDistribution: make([]*protocol.TimeDistributionItem, len(stats.TimeDistribution)),
		RecentActivities: make([]*protocol.RecentActivityItem, len(stats.RecentActivities)),
	}

	// Convert top actors
	for i, actor := range stats.TopActors {
		protocolStats.TopActors[i] = &protocol.ActorStatistics{
			ActorUserId: actor.ActorUserId,
			ActionCount: actor.ActionCount,
			LastAction:  actor.LastAction,
		}
	}

	// Convert top entities
	for i, entity := range stats.TopEntities {
		protocolStats.TopEntities[i] = &protocol.EntityStatistics{
			EntityId:    entity.EntityId,
			ActionCount: entity.ActionCount,
			LastAction:  entity.LastAction,
		}
	}

	// Convert time distribution
	for i, item := range stats.TimeDistribution {
		protocolStats.TimeDistribution[i] = &protocol.TimeDistributionItem{
			TimeSlot: item.TimeSlot,
			Count:    item.Count,
		}
	}

	// Convert recent activities
	for i, activity := range stats.RecentActivities {
		protocolStats.RecentActivities[i] = &protocol.RecentActivityItem{
			ActionType:       activity.ActionType,
			ActorUserId:      activity.ActorUserId,
			AffectedEntityId: activity.AffectedEntityId,
			ActionTime:       activity.ActionTime,
		}
	}

	return protocolStats
}
