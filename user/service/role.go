package service

import (
	"context"
	"hotel/common/bff"
	"hotel/common/pagehelper"
	"hotel/user/protocol"
)

// ListRole
// @desc: 展示角色列表
// @path: /listRole
// @tags: tenant
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
func (s *TenantService) ListRole(ctx context.Context, req *protocol.ListRoleReq) (*protocol.ListRoleResp, error) {
	return s.UserService.ListRole(ctx, req)
}

// UpsertRole
// @desc: 新建角色
// @path: /upsertRole
// @tags: tenant
// @auth: required
// @param: EntityIDs 仅返回登录用户有权限的entity，越权暂不返回错误
// @response: 404,UserNotFound,"用户不存在"
func (s *TenantService) UpsertRole(ctx context.Context, req *protocol.CreateRoleReq) error {
	return s.UserService.UpsertRole(ctx, req)
}

func (s *UserService) ListRole(ctx context.Context, req *protocol.ListRoleReq) (*protocol.ListRoleResp, error) {
	roles, err := s.dao.Role.GetRolesByScope(ctx, req.Scope)
	if err != nil {
		return nil, err
	}

	var ffRoles []*bff.ElementRow[protocol.UserRoleRow]

	for _, role := range roles {
		ffRoles = append(ffRoles, &bff.ElementRow[protocol.UserRoleRow]{
			Raw: protocol.UserRoleRow{
				Role: role,
			},
			Key:     role.Name,
			Columns: nil, //todo: format
		})
	}

	return &protocol.ListRoleResp{
		bff.Table[protocol.UserRoleRow]{
			HeaderKeys: []string{"id", "name", "privileges", "scope"},
			//Rows:       roles,
			PageResp: pagehelper.PageResp{Total: int64(len(roles))},
		}}, nil
}
