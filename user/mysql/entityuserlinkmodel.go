package mysql

import (
	"context"
	"fmt"
	jsqlx "github.com/jmoiron/sqlx"
	"github.com/zeromicro/go-zero/core/stores/sqlx"
)

type (
	EntityUserLinkModel struct {
		*defaultEntityUserLinkModel
	}
)

// NewEntityUserLinkModel returns a model for the database table.
func NewEntityUserLinkModel(conn sqlx.SqlConn) *EntityUserLinkModel {
	return &EntityUserLinkModel{
		defaultEntityUserLinkModel: newEntityUserLinkModel(conn),
	}
}

func (m *EntityUserLinkModel) withSession(session sqlx.Session) *EntityUserLinkModel {
	return NewEntityUserLinkModel(sqlx.NewSqlConnFromSession(session))
}

func (m *EntityUserLinkModel) FindByUserIdEntityIds(ctx context.Context, userId int64, entityIds []int64) ([]*EntityUserLink, error) {
	var resp []*EntityUserLink
	baseQuery := fmt.Sprintf("select %s from %s where `user_id` = ? and `entity_id` in (?)", entityUserLinkRows, m.table)
	query, args, err := jsqlx.In(baseQuery, userId, entityIds)
	if err != nil {
		return nil, err
	}
	err = m.conn.QueryRowsCtx(ctx, &resp, query, args...)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

func (m *EntityUserLinkModel) ListAllByPage(ctx context.Context, cursor int64) ([]*EntityUserLink, int64, error) {
	var resp []*EntityUserLink
	baseQuery := fmt.Sprintf("select %s from %s where `id` > ? ORDER BY id ASC", entityUserLinkRows, m.table)
	err := m.conn.QueryRowsCtx(ctx, &resp, baseQuery, cursor)
	if err != nil {
		return nil, 0, err
	}
	if len(resp) == 0 {
		return nil, 0, nil
	}
	return resp, resp[len(resp)-1].Id, nil
}
