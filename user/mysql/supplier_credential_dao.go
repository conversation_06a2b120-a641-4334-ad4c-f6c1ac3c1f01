package mysql

import (
	"context"

	"hotel/common/bizerr"
	supplierDomain "hotel/supplier/domain"
	"hotel/user/domain"
)

type SupplierCredentialDao struct {
	m *SupplierCredentialModel
}

func NewSupplierCredentialDao(m *SupplierCredentialModel) *SupplierCredentialDao {
	return &SupplierCredentialDao{m: m}
}

// todo: cache
func (s *SupplierCredentialDao) Get(ctx context.Context, id int64) (*domain.Credential, error) {
	d, err := s.m.FindOne(ctx, id)
	if err != nil {
		return nil, err
	}
	return convertSupplierCredentialModel2Domain(d), nil
}

func (s *SupplierCredentialDao) Upsert(ctx context.Context, in *domain.Credential) error {
	_, err := s.m.FindOne(ctx, in.CredentialId)
	if bizerr.NotFoundErr.Is(err) {
		_, err = s.m.Insert(ctx, convertSupplierCredentialDomain2Model(in))
		return err
	}
	if err != nil {
		return err
	}

	return s.m.Update(ctx, convertSupplierCredentialDomain2Model(in))
}

func convertSupplierCredentialDomain2Model(d *domain.Credential) *SupplierCredential {
	if d == nil {
		return nil
	}
	return &SupplierCredential{
		Id:          d.CredentialId,
		Supplier:    uint64(d.Supplier),
		Credentials: d.CredentialContent,
	}
}

func convertSupplierCredentialModel2Domain(d *SupplierCredential) *domain.Credential {
	if d == nil {
		return nil
	}
	return &domain.Credential{
		Supplier:          supplierDomain.Supplier(d.Supplier),
		CredentialId:      d.Id,
		CredentialContent: d.Credentials,
	}
}
