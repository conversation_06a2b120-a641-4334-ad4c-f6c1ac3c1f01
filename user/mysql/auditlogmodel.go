package mysql

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"hotel/common/pagehelper"
	"time"

	"github.com/zeromicro/go-zero/core/stores/sqlx"

	"hotel/common/types"
	"hotel/common/utils"
	"hotel/user/domain"
)

type (
	AuditLogModel struct {
		*defaultAuditLogModel
	}
)

// NewAuditLogModel returns a model for the database table.
func NewAuditLogModel(conn sqlx.SqlConn) *AuditLogModel {
	return &AuditLogModel{
		defaultAuditLogModel: newAuditLogModel(conn),
	}
}

func (m *AuditLogModel) withSession(session sqlx.Session) *AuditLogModel {
	return NewAuditLogModel(sqlx.NewSqlConnFromSession(session))
}

func (m *AuditLogModel) QueryByActionTypeAndTime(ctx context.Context, actionType string, actionTimeStart, actionTimeEnd time.Time, pageReq pagehelper.PageReq) ([]*AuditLog, error) {
	var resp []*AuditLog
	// 使用 sqlx.In 处理数组参数
	baseQuery := fmt.Sprintf("SELECT %s FROM %s WHERE `action_type`=? AND `action_time`>? AND `action_time`<=? ORDER BY `action_time` DESC LIMIT ?,?", auditLogRows, m.table)

	err := m.conn.QueryRowsCtx(ctx, &resp, baseQuery, actionType, actionTimeStart, actionTimeEnd, pageReq.GetOffset(), pageReq.PageSize)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlx.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

func convert2ModelAuditLog(in *domain.AuditLog) *AuditLog {
	if in == nil {
		return nil
	}
	return &AuditLog{
		Id:                 in.Id,
		ActionType:         in.ActionType,
		ActionTime:         in.ActionTime,
		ActorUserId:        in.ActorUserId.Int64(),
		ActorInfo:          in.ActorInfo,
		AffectedEntityId:   in.AffectedEntityId.Int64(),
		AffectedEntityInfo: in.AffectedEntityInfo,
		Details:            sql.NullString{String: utils.ToLogString(in.Details), Valid: true},
	}
}

func convert2DomainAuditLog(in *AuditLog) *domain.AuditLog {
	if in == nil {
		return nil
	}
	return &domain.AuditLog{
		Id:                 in.Id,
		ActionType:         in.ActionType,
		ActionTime:         in.ActionTime,
		ActorUserId:        types.ID(in.ActorUserId),
		ActorInfo:          in.ActorInfo,
		AffectedEntityId:   types.ID(in.AffectedEntityId),
		AffectedEntityInfo: in.AffectedEntityInfo,
		Details:            utils.FromJSON[domain.AuditLogDetail](in.Details.String),
	}
}

// QueryAuditLogs represents the advanced query method for audit logs with multiple filters
func (m *AuditLogModel) QueryAuditLogs(ctx context.Context, req *domain.AuditLogQuery) ([]*AuditLog, error) {
	var resp []*AuditLog
	var args []interface{}

	// Build dynamic query
	query := fmt.Sprintf("SELECT %s FROM %s WHERE 1=1", auditLogRows, m.table)

	if req.ActionType != "" {
		query += " AND `action_type` = ?"
		args = append(args, req.ActionType)
	}

	if req.ActionTimeStart != nil {
		query += " AND `action_time` >= ?"
		args = append(args, *req.ActionTimeStart)
	}

	if req.ActionTimeEnd != nil {
		query += " AND `action_time` <= ?"
		args = append(args, *req.ActionTimeEnd)
	}

	if req.ActorUserId != nil {
		query += " AND `actor_user_id` = ?"
		args = append(args, *req.ActorUserId)
	}

	if req.AffectedEntityId != nil {
		query += " AND `affected_entity_id` = ?"
		args = append(args, *req.AffectedEntityId)
	}

	if req.Keyword != "" {
		query += " AND (`actor_info` LIKE ? OR `affected_entity_info` LIKE ? OR `details` LIKE ?)"
		keyword := "%" + req.Keyword + "%"
		args = append(args, keyword, keyword, keyword)
	}

	// Add sorting
	if req.SortBy != "" {
		query += fmt.Sprintf(" ORDER BY `%s`", req.SortBy)
		if req.SortOrder == "desc" {
			query += " DESC"
		} else {
			query += " ASC"
		}
	} else {
		query += " ORDER BY `action_time` DESC"
	}

	// Add pagination
	if req.Limit > 0 {
		query += " LIMIT ?, ?"
		args = append(args, req.Offset, req.Limit)
	}

	err := m.conn.QueryRowsCtx(ctx, &resp, query, args...)
	switch {
	case err == nil:
		return resp, nil
	case errors.Is(err, sqlx.ErrNotFound):
		return nil, ErrNotFound
	default:
		return nil, err
	}
}

// CountAuditLogs represents the method to count audit logs with filters
func (m *AuditLogModel) CountAuditLogs(ctx context.Context, req *domain.AuditLogQuery) (int64, error) {
	var count int64
	var args []interface{}

	// Build dynamic query
	query := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE 1=1", m.table)

	if req.ActionType != "" {
		query += " AND `action_type` = ?"
		args = append(args, req.ActionType)
	}

	if req.ActionTimeStart != nil {
		query += " AND `action_time` >= ?"
		args = append(args, *req.ActionTimeStart)
	}

	if req.ActionTimeEnd != nil {
		query += " AND `action_time` <= ?"
		args = append(args, *req.ActionTimeEnd)
	}

	if req.ActorUserId != nil {
		query += " AND `actor_user_id` = ?"
		args = append(args, *req.ActorUserId)
	}

	if req.AffectedEntityId != nil {
		query += " AND `affected_entity_id` = ?"
		args = append(args, *req.AffectedEntityId)
	}

	if req.Keyword != "" {
		query += " AND (`actor_info` LIKE ? OR `affected_entity_info` LIKE ? OR `details` LIKE ?)"
		keyword := "%" + req.Keyword + "%"
		args = append(args, keyword, keyword, keyword)
	}

	err := m.conn.QueryRowCtx(ctx, &count, query, args...)
	if err != nil {
		return 0, err
	}

	return count, nil
}

// GetAuditLogStatistics represents the method to get audit log statistics
func (m *AuditLogModel) GetAuditLogStatistics(ctx context.Context, req *domain.AuditLogQuery) (*domain.AuditLogStatistics, error) {
	stats := &domain.AuditLogStatistics{
		ActionTypeCount:  make(map[string]int64),
		TopActors:        make([]*domain.ActorStatistics, 0),
		TopEntities:      make([]*domain.EntityStatistics, 0),
		TimeDistribution: make([]*domain.TimeDistributionItem, 0),
		RecentActivities: make([]*domain.RecentActivityItem, 0),
	}

	// Get total count
	totalCount, err := m.CountAuditLogs(ctx, req)
	if err != nil {
		return nil, err
	}
	stats.TotalCount = totalCount

	// Get action type count
	actionTypeQuery := fmt.Sprintf("SELECT `action_type`, COUNT(*) as count FROM %s WHERE 1=1", m.table)
	var actionTypeArgs []interface{}

	if req.ActionTimeStart != nil {
		actionTypeQuery += " AND `action_time` >= ?"
		actionTypeArgs = append(actionTypeArgs, *req.ActionTimeStart)
	}

	if req.ActionTimeEnd != nil {
		actionTypeQuery += " AND `action_time` <= ?"
		actionTypeArgs = append(actionTypeArgs, *req.ActionTimeEnd)
	}

	actionTypeQuery += " GROUP BY `action_type` ORDER BY count DESC LIMIT 10"

	type ActionTypeCount struct {
		ActionType string `db:"action_type"`
		Count      int64  `db:"count"`
	}

	var actionTypeCounts []ActionTypeCount
	err = m.conn.QueryRowsCtx(ctx, &actionTypeCounts, actionTypeQuery, actionTypeArgs...)
	if err == nil {
		for _, item := range actionTypeCounts {
			stats.ActionTypeCount[item.ActionType] = item.Count
		}
	}

	// Get top actors
	topActorsQuery := fmt.Sprintf("SELECT `actor_user_id`, COUNT(*) as count, MAX(`action_time`) as last_action FROM %s WHERE 1=1", m.table)
	var topActorsArgs []interface{}

	if req.ActionTimeStart != nil {
		topActorsQuery += " AND `action_time` >= ?"
		topActorsArgs = append(topActorsArgs, *req.ActionTimeStart)
	}

	if req.ActionTimeEnd != nil {
		topActorsQuery += " AND `action_time` <= ?"
		topActorsArgs = append(topActorsArgs, *req.ActionTimeEnd)
	}

	topActorsQuery += " GROUP BY `actor_user_id` ORDER BY count DESC LIMIT 10"

	type TopActor struct {
		ActorUserId int64  `db:"actor_user_id"`
		Count       int64  `db:"count"`
		LastAction  string `db:"last_action"`
	}

	var topActors []TopActor
	err = m.conn.QueryRowsCtx(ctx, &topActors, topActorsQuery, topActorsArgs...)
	if err == nil {
		for _, item := range topActors {
			stats.TopActors = append(stats.TopActors, &domain.ActorStatistics{
				ActorUserId: item.ActorUserId,
				ActionCount: item.Count,
				LastAction:  item.LastAction,
			})
		}
	}

	// Get top entities
	topEntitiesQuery := fmt.Sprintf("SELECT `affected_entity_id`, COUNT(*) as count, MAX(`action_time`) as last_action FROM %s WHERE 1=1", m.table)
	var topEntitiesArgs []interface{}

	if req.ActionTimeStart != nil {
		topEntitiesQuery += " AND `action_time` >= ?"
		topEntitiesArgs = append(topEntitiesArgs, *req.ActionTimeStart)
	}

	if req.ActionTimeEnd != nil {
		topEntitiesQuery += " AND `action_time` <= ?"
		topEntitiesArgs = append(topEntitiesArgs, *req.ActionTimeEnd)
	}

	topEntitiesQuery += " GROUP BY `affected_entity_id` ORDER BY count DESC LIMIT 10"

	type TopEntity struct {
		EntityId   int64  `db:"affected_entity_id"`
		Count      int64  `db:"count"`
		LastAction string `db:"last_action"`
	}

	var topEntities []TopEntity
	err = m.conn.QueryRowsCtx(ctx, &topEntities, topEntitiesQuery, topEntitiesArgs...)
	if err == nil {
		for _, item := range topEntities {
			stats.TopEntities = append(stats.TopEntities, &domain.EntityStatistics{
				EntityId:    item.EntityId,
				ActionCount: item.Count,
				LastAction:  item.LastAction,
			})
		}
	}

	// Get recent activities
	recentActivitiesQuery := fmt.Sprintf("SELECT `action_type`, `actor_user_id`, `affected_entity_id`, `action_time` FROM %s WHERE 1=1", m.table)
	var recentActivitiesArgs []interface{}

	if req.ActionTimeStart != nil {
		recentActivitiesQuery += " AND `action_time` >= ?"
		recentActivitiesArgs = append(recentActivitiesArgs, *req.ActionTimeStart)
	}

	if req.ActionTimeEnd != nil {
		recentActivitiesQuery += " AND `action_time` <= ?"
		recentActivitiesArgs = append(recentActivitiesArgs, *req.ActionTimeEnd)
	}

	recentActivitiesQuery += " ORDER BY `action_time` DESC LIMIT 20"

	type RecentActivity struct {
		ActionType       string `db:"action_type"`
		ActorUserId      int64  `db:"actor_user_id"`
		AffectedEntityId int64  `db:"affected_entity_id"`
		ActionTime       string `db:"action_time"`
	}

	var recentActivities []RecentActivity
	err = m.conn.QueryRowsCtx(ctx, &recentActivities, recentActivitiesQuery, recentActivitiesArgs...)
	if err == nil {
		for _, item := range recentActivities {
			stats.RecentActivities = append(stats.RecentActivities, &domain.RecentActivityItem{
				ActionType:       item.ActionType,
				ActorUserId:      item.ActorUserId,
				AffectedEntityId: item.AffectedEntityId,
				ActionTime:       item.ActionTime,
			})
		}
	}

	return stats, nil
}
