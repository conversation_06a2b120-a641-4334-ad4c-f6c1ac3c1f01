package mysql

import (
	"context"
	"hotel/common/pagehelper"
	"time"

	pkgerr "github.com/pkg/errors"

	"hotel/user/domain"
)

type AuditLogDao struct {
	auditLogModel *AuditLogModel
}

// QueryByActionTypeAndTime represents the enhanced query method for audit logs with advanced filtering
func (m *AuditLogDao) QueryByActionTypeAndTime(ctx context.Context, actionType string, actionTimeStart, actionTimeEnd time.Time, pageReq pagehelper.PageReq) ([]*domain.AuditLog, error) {
	t, err := m.auditLogModel.QueryByActionTypeAndTime(ctx, actionType, actionTimeStart, actionTimeEnd, pageReq)
	if err != nil {
		return nil, pkgerr.Wrap(err, "QueryByActionTypeAndTime")
	}
	var resp []*domain.AuditLog
	for _, in := range t {
		resp = append(resp, convert2DomainAuditLog(in))
	}
	return resp, nil
}

// QueryAuditLogs represents the advanced query method for audit logs with multiple filters
func (m *AuditLogDao) QueryAuditLogs(ctx context.Context, req *domain.AuditLogQuery) ([]*domain.AuditLog, error) {
	t, err := m.auditLogModel.QueryAuditLogs(ctx, req)
	if err != nil {
		return nil, pkgerr.Wrap(err, "QueryAuditLogs")
	}
	var resp []*domain.AuditLog
	for _, in := range t {
		resp = append(resp, convert2DomainAuditLog(in))
	}
	return resp, nil
}

// GetAuditLogStatistics represents the method to get audit log statistics
func (m *AuditLogDao) GetAuditLogStatistics(ctx context.Context, req *domain.AuditLogQuery) (*domain.AuditLogStatistics, error) {
	stats, err := m.auditLogModel.GetAuditLogStatistics(ctx, req)
	if err != nil {
		return nil, pkgerr.Wrap(err, "GetAuditLogStatistics")
	}
	return stats, nil
}

// CountAuditLogs represents the method to count audit logs with filters
func (m *AuditLogDao) CountAuditLogs(ctx context.Context, req *domain.AuditLogQuery) (int64, error) {
	count, err := m.auditLogModel.CountAuditLogs(ctx, req)
	if err != nil {
		return 0, pkgerr.Wrap(err, "CountAuditLogs")
	}
	return count, nil
}
func (m *AuditLogDao) Insert(ctx context.Context, in *domain.AuditLog) error {
	v := convert2ModelAuditLog(in)
	_, err := m.auditLogModel.Insert(ctx, v)
	return err
}

func newAuditLogDao(auditLogModel *AuditLogModel) *AuditLogDao {
	return &AuditLogDao{
		auditLogModel: auditLogModel,
	}
}
