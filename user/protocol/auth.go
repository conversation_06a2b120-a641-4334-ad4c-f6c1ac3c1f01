package protocol

import (
	"time"

	"hotel/common/bff"
	domain2 "hotel/common/domain"
	"hotel/common/i18n"
	"hotel/user/domain"
)

// GetUserReq represents the request structure for user retrieval operation with 1 fields
type GetUserReq struct {
	Key string `json:"key"` // Key is the unique identifier for this user
}

// GetUserResp represents the response structure for user retrieval operation with 4 fields
type GetUserResp struct {
	User *domain.User `json:"user,omitzero"` // User contains the user data for this retrieval
	// 这个里面会放当前用户所在的entity下的所有role
	// BFF
	Basic   bff.Table[UserDetailBasic]   `json:"basic,omitzero"`   // Basic contains the basic user details
	Profile bff.Table[UserDetailProfile] `json:"profile,omitzero"` // Profile contains the user profile details
	Role    bff.Table[domain.User]       `json:"role,omitzero"`    // Role contains the user role information
}

// UserDetailBasic represents a data structure for API communication with 3 fields
type UserDetailBasic struct {
	Organization i18n.I18N         `json:"organization"` // Organization contains the organization information
	UserType     i18n.I18N         `json:"userType"`     // UserType contains the user type information
	InvitedBy    *domain.UserBasic `json:"invitedBy"`    // InvitedBy contains the inviter user data
}

// UserDetailProfile represents a data structure for API communication with 2 fields
type UserDetailProfile struct {
	Phone       domain2.Phone `json:"phone,omitzero"`       // Phone contains the phone number for this profile
	Designation i18n.I18N     `json:"designation,omitzero"` // Designation contains the designation information
}

// SendOTPReq represents the request structure for OTP sending operation with 1 fields
type SendOTPReq struct {
	Email string `json:"email"` // Email is the email address for this OTP
}

// SendOTPResp represents the response structure for OTP sending operation with 1 fields
type SendOTPResp struct {
	NextTime time.Time `json:"nextTime"` // NextTime represents the timestamp when this OTP can be resent
}

// VerifyOTPReq represents the request structure for OTP verification operation with 2 fields
type VerifyOTPReq struct {
	Email string `json:"email"` // Email is the email address for this verification
	Code  string `json:"code"`  // Code contains the OTP code value
}

// VerifyOTPResp represents the response structure for OTP verification operation with 0 fields
type VerifyOTPResp struct {
}

// RegisterUserReq represents the request structure for user registration operation with 2 fields
type RegisterUserReq struct {
	*domain.User        // User contains the user data for this registration
	Link         string `json:"link"` // Link contains the registration link value
}

// RegisterUserResp represents the response structure for user registration operation with 0 fields
type RegisterUserResp struct {
}
