package protocol

import (
	"hotel/common/bff"
	"hotel/common/pagehelper"
	"hotel/common/types"
	"hotel/user/domain"
)

type AddAuditLogReq struct {
	Log      *domain.AuditLog
	Operator *domain.User `json:"operator" apidoc:"-"`
}

type AddAuditLogResp struct {
}

// ListAuditLogReq represents the request structure for audit log listing operation with 8 fields
type ListAuditLogReq struct {
	ActionType         string           `json:"actionType,omitempty"`       // ActionType contains the action type value for filtering
	ActionTime         types.TimeWindow `json:"actionTime,omitempty"`       // ActionTime contains the time window for filtering
	ActorUserId        *int64           `json:"actorUserId,omitempty"`      // ActorUserId represents the actor user identifier as an integer for filtering
	AffectedEntityId   *int64           `json:"affectedEntityId,omitempty"` // AffectedEntityId represents the affected entity identifier as an integer for filtering
	Keyword            string           `json:"keyword,omitempty"`          // Keyword contains the search keyword value
	IncludeDetails     bool             `json:"includeDetails,omitempty"`   // IncludeDetails indicates whether to include detailed information
	SortBy             string           `json:"sortBy,omitempty"`           // SortBy contains the sort field value
	SortOrder          string           `json:"sortOrder,omitempty"`        // SortOrder contains the sort order value (asc/desc)
	pagehelper.PageReq                  // PageReq contains the pagination request data
}

// ListAuditLogResp represents the response structure for audit log listing operation with 2 fields
type ListAuditLogResp struct {
	bff.Table[domain.AuditLog]                     // Table contains the audit log table data
	Statistics                 *AuditLogStatistics `json:"statistics,omitempty"` // Statistics contains the audit log statistics data
}

// AuditLogStatistics represents a data structure for API communication with 6 fields
type AuditLogStatistics struct {
	TotalCount       int64                   `json:"totalCount"`       // TotalCount represents the total number as an integer
	ActionTypeCount  map[string]int64        `json:"actionTypeCount"`  // ActionTypeCount contains the action type count data
	TopActors        []*ActorStatistics      `json:"topActors"`        // TopActors contains a list of top actor statistics items
	TopEntities      []*EntityStatistics     `json:"topEntities"`      // TopEntities contains a list of top entity statistics items
	TimeDistribution []*TimeDistributionItem `json:"timeDistribution"` // TimeDistribution contains a list of time distribution items
	RecentActivities []*RecentActivityItem   `json:"recentActivities"` // RecentActivities contains a list of recent activity items
}

// ActorStatistics represents a data structure for API communication with 3 fields
type ActorStatistics struct {
	ActorUserId int64  `json:"actorUserId"` // ActorUserId represents the actor user identifier as an integer
	ActionCount int64  `json:"actionCount"` // ActionCount represents the action count as an integer
	LastAction  string `json:"lastAction"`  // LastAction contains the last action value
}

// EntityStatistics represents a data structure for API communication with 3 fields
type EntityStatistics struct {
	EntityId    int64  `json:"entityId"`    // EntityId represents the entity identifier as an integer
	ActionCount int64  `json:"actionCount"` // ActionCount represents the action count as an integer
	LastAction  string `json:"lastAction"`  // LastAction contains the last action value
}

// TimeDistributionItem represents a data structure for API communication with 2 fields
type TimeDistributionItem struct {
	TimeSlot string `json:"timeSlot"` // TimeSlot contains the time slot value
	Count    int64  `json:"count"`    // Count represents the count as an integer
}

// RecentActivityItem represents a data structure for API communication with 4 fields
type RecentActivityItem struct {
	ActionType       string `json:"actionType"`       // ActionType contains the action type value
	ActorUserId      int64  `json:"actorUserId"`      // ActorUserId represents the actor user identifier as an integer
	AffectedEntityId int64  `json:"affectedEntityId"` // AffectedEntityId represents the affected entity identifier as an integer
	ActionTime       string `json:"actionTime"`       // ActionTime contains the action time value
}
