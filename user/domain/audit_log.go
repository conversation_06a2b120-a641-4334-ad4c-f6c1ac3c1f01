package domain

import (
	"time"

	"hotel/common/types"
)

type AuditLog struct {
	Id                 uint64         `json:"id"`                 // 主键ID
	ActionType         string         `json:"actionType"`         // 操作类型: USER_INVITE, ROLE_ASSIGN, DEACTIVATION, IMPERSONATION...
	ActionTime         time.Time      `json:"actionTime"`         // 操作时间
	ActorUserId        types.ID       `json:"actorUserId"`        // 操作者user.id
	ActorInfo          string         `json:"actorInfo"`          // 操作者信息(JSON)，如: {"name":"张三","email":"<EMAIL>"}
	AffectedEntityId   types.ID       `json:"affectedEntityId"`   // 被操作实体ID
	AffectedEntityInfo string         `json:"affectedEntityInfo"` // 被操作实体信息(JSON)
	Details            AuditLogDetail `json:"details"`            // 操作详情(JSON)，如: {"old_role":"guest","new_role":"admin"}

}

// AuditLogDetail represents a data structure for API communication with 1 fields
type AuditLogDetail struct {
	Raw interface{} `json:"raw"` // Raw contains the raw audit log detail data
}

// AuditLogQuery represents the query parameters for audit log search
type AuditLogQuery struct {
	ActionType       string     `json:"actionType,omitempty"`       // ActionType contains the action type value for filtering
	ActionTimeStart  *time.Time `json:"actionTimeStart,omitempty"`  // ActionTimeStart represents the start time for filtering
	ActionTimeEnd    *time.Time `json:"actionTimeEnd,omitempty"`    // ActionTimeEnd represents the end time for filtering
	ActorUserId      *int64     `json:"actorUserId,omitempty"`      // ActorUserId represents the actor user identifier as an integer for filtering
	AffectedEntityId *int64     `json:"affectedEntityId,omitempty"` // AffectedEntityId represents the affected entity identifier as an integer for filtering
	Keyword          string     `json:"keyword,omitempty"`          // Keyword contains the search keyword value
	IncludeDetails   bool       `json:"includeDetails,omitempty"`   // IncludeDetails indicates whether to include detailed information
	SortBy           string     `json:"sortBy,omitempty"`           // SortBy contains the sort field value
	SortOrder        string     `json:"sortOrder,omitempty"`        // SortOrder contains the sort order value (asc/desc)
	Offset           int        `json:"offset,omitempty"`           // Offset represents the offset as an integer for pagination
	Limit            int        `json:"limit,omitempty"`            // Limit represents the limit as an integer for pagination
}

// AuditLogStatistics represents the statistics for audit logs
type AuditLogStatistics struct {
	TotalCount       int64                   `json:"totalCount"`       // TotalCount represents the total number as an integer
	ActionTypeCount  map[string]int64        `json:"actionTypeCount"`  // ActionTypeCount contains the action type count data
	TopActors        []*ActorStatistics      `json:"topActors"`        // TopActors contains a list of top actor statistics items
	TopEntities      []*EntityStatistics     `json:"topEntities"`      // TopEntities contains a list of top entity statistics items
	TimeDistribution []*TimeDistributionItem `json:"timeDistribution"` // TimeDistribution contains a list of time distribution items
	RecentActivities []*RecentActivityItem   `json:"recentActivities"` // RecentActivities contains a list of recent activity items
}

// ActorStatistics represents the statistics for actors
type ActorStatistics struct {
	ActorUserId int64  `json:"actorUserId"` // ActorUserId represents the actor user identifier as an integer
	ActionCount int64  `json:"actionCount"` // ActionCount represents the action count as an integer
	LastAction  string `json:"lastAction"`  // LastAction contains the last action value
}

// EntityStatistics represents the statistics for entities
type EntityStatistics struct {
	EntityId    int64  `json:"entityId"`    // EntityId represents the entity identifier as an integer
	ActionCount int64  `json:"actionCount"` // ActionCount represents the action count as an integer
	LastAction  string `json:"lastAction"`  // LastAction contains the last action value
}

// TimeDistributionItem represents the time distribution statistics
type TimeDistributionItem struct {
	TimeSlot string `json:"timeSlot"` // TimeSlot contains the time slot value
	Count    int64  `json:"count"`    // Count represents the count as an integer
}

// RecentActivityItem represents the recent activity statistics
type RecentActivityItem struct {
	ActionType       string `json:"actionType"`       // ActionType contains the action type value
	ActorUserId      int64  `json:"actorUserId"`      // ActorUserId represents the actor user identifier as an integer
	AffectedEntityId int64  `json:"affectedEntityId"` // AffectedEntityId represents the affected entity identifier as an integer
	ActionTime       string `json:"actionTime"`       // ActionTime contains the action time value
}
