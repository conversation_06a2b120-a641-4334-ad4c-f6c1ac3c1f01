package dispatch

import (
	"context"
	"fmt"
	"hotel/common/utils"
	"time"

	"go.uber.org/atomic"

	"hotel/common/log"
	"hotel/common/metrics"
)

type stat struct {
	inCnt    atomic.Int64
	dedupCnt atomic.Int64
	errCnt   atomic.Int64
	sucCnt   atomic.Int64
}

func (s *stat) Pretty() string {
	return fmt.Sprintf("Input(%d) Output(%d) Duplicated(%d) Err(%d)", s.inCnt.Load(), s.sucCnt.Load(), s.dedupCnt.Load(), s.errCnt.Load())
}

type option struct {
	name           string
	ctx            context.Context
	chunkSize      int
	ignoreErr      bool
	consumer       func([]Item) error
	enableDedupLog bool
}

func (o *option) fix() {
	if o.chunkSize <= 0 {
		o.chunkSize = 10
	}
	if o.name == "" {
		o.name = "annoymous"
	}
	if o.ctx == nil {
		o.ctx = context.Background()
	}
}

type Option func(o *option)

func Context(ctx context.Context) Option {
	return func(o *option) {
		o.ctx = ctx
	}
}
func Name(n string) Option {
	return func(o *option) {
		o.name = n
	}
}
func ChunkSize(n int) Option {
	return func(o *option) {
		o.chunkSize = n
	}
}
func IgnoreError(n bool) Option {
	return func(o *option) {
		o.ignoreErr = n
	}
}
func Consume(consumer func([]Item) error) Option {
	return func(o *option) {
		o.consumer = consumer
	}
}

type Dispatcher struct {
	list []Item
	m    map[string]Item
	opt  *option
	stat *stat
}

type Item interface {
	GroupBy() string
	GetUniqueID() string
}

func NewDispatcher(list []Item, opts ...Option) *Dispatcher {
	o := new(option)
	for _, opt := range opts {
		opt(o)
	}
	o.fix()
	return (&Dispatcher{opt: o, stat: new(stat), m: map[string]Item{}}).Append(list...)
}

func (d *Dispatcher) Append(list ...Item) *Dispatcher {
	for _, v := range list {
		d.stat.inCnt.Inc()
		uid := v.GetUniqueID()
		if _, ok := d.m[uid]; ok {
			if d.opt.enableDedupLog {
				_metricsDedup.WithLogf("uniqueID(%v)", uid).Incr(d.opt.ctx, d.opt.name)
			} else {
				_metricsDedup.Incr(d.opt.ctx, d.opt.name)
			}
			d.stat.dedupCnt.Inc()
			continue
		}
		d.m[uid] = v
		d.list = append(d.list, v)
	}
	return d
}

func (d *Dispatcher) Run() error {
	return d.Consume(d.opt.consumer)
}

func (d *Dispatcher) Consume(f func([]Item) error) error {
	st := time.Now()

	m := make(map[string][]Item)
	for _, v := range d.list {
		grp := v.GroupBy()
		m[grp] = append(m[grp], v)
	}

	_metricsGroupCount.IncrN(d.opt.ctx, int64(len(m)), d.opt.name)

	for _, v := range m {
		_metricsGroupSize.Timing(d.opt.ctx, int64(len(v)), d.opt.name)

		chunks := Chunk(v, d.opt.chunkSize)
		for _, chunk := range chunks {
			if err := f(chunk); err != nil {
				d.stat.errCnt.Add(int64(len(chunk)))
				_metricsConsumeErr.Incr(d.opt.ctx, d.opt.name)
				if !d.opt.ignoreErr {
					return err
				}
			} else {
				d.stat.sucCnt.Add(int64(len(chunk)))
			}
		}
	}
	log.Infoc(d.opt.ctx, "Dispatch(%s) Channel Stat:%s Cost:%s", d.opt.name, d.stat.Pretty(), time.Since(st))
	return nil
}

func Chunk(slice []Item, size int) (chunks [][]Item) {
	return utils.Chunk[Item](slice, size)
}

var (
	_metricsDedup      = metrics.New().WithCounter("gocommon_dispatch_dedup", []string{"name"})
	_metricsGroupCount = metrics.New().WithCounter("gocommon_dispatch_group_count", []string{"name"})
	_metricsGroupSize  = metrics.New().WithTimer("gocommon_dispatch_group_size", []string{"name"})
	_metricsConsumeErr = metrics.New().WithCounter("gocommon_dispatch_consume_err", []string{"name"})
)
