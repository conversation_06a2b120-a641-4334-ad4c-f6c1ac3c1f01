package quota // 或者你的初始化包

import (
	"hotel/common/log"
	"time"

	"github.com/Danceiny/sentinel-golang/api"
	"github.com/Danceiny/sentinel-golang/core/flow"
)

// MySQL 配置信息
const (
	pollInterval = 5 * time.Minute // 定义轮询间隔，例如 5 秒
)

func InitWithDatasource(d *Datasource) error { // 返回 DataSource 以便管理生命周期
	// 1. 初始化 Sentinel Core API
	err := api.InitDefault()
	if err != nil {
		return err
	}

	// 3. 首次加载规则
	err = loadRulesFromDatasource(d)
	if err != nil {
		// 根据需要决定是否在首次加载失败时退出程序
		return err
	}

	// 4. 启动后台 goroutine 定期轮询加载规则
	go pollRulesFromDatasource(d)

	go func() {
		for rule := range d.Channel {
			_, err := flow.LoadRules([]*flow.Rule{rule})
			if err != nil {
				log.Error("Error loading flow rules into Sentinel: %v", err)
			}
		}
	}()
	return nil // 返回创建的实例
}

// loadRulesFromMySQL 从 MySQL 加载一次规则
func loadRulesFromDatasource(d *Datasource) error {
	var rules []*flow.Rule
	for rule := range d.Channel {
		rules = append(rules, rule)
	}

	_, err := flow.LoadRules(rules)
	if err != nil {
		log.Error("Error loading flow rules into Sentinel: %v", err)
		return err
	}
	return nil
}

// pollRulesFromDatasource 定期从 MySQL 拉取并加载规则
func pollRulesFromDatasource(ds *Datasource) {
	ticker := time.NewTicker(pollInterval)
	defer ticker.Stop()

	for range ticker.C {
		err := loadRulesFromDatasource(ds)
		if err != nil {
			// 轮询过程中的错误通常只记录日志，不中断程序
			log.Error("Error during rule polling: %v", err)
		}
	}
}
