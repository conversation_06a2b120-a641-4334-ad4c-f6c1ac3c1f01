package httpdispatcher

import (
	"fmt"
	"net/http"
	"time"

	// flow 包在这里不再直接需要，因为规则由 datasource 管理
	"hotel/common/log"
	"hotel/common/utils"

	"hotel/common/metrics"
)

// Dispatch Core implementation (确保 sentinelAPIGatewayMiddleware 在正确位置)
func (s *ServiceDispatcher) Dispatch(w http.ResponseWriter, r *http.Request) {
	ctx := &Context{
		Request:  r,
		Response: w,
		Start:    time.Now(),
	}
	var err error
	defer func() {
		ctx.End = time.Now()
		log.Info("%s", utils.ToJSON(
			map[string]interface{}{
				"path":     ctx.Request.URL.Path,
				"service":  ctx.RouteInfo.Service,
				"method":   ctx.RouteInfo.Method,
				"err":      fmt.Sprint(err),
				"input":    ctx.Input,
				"output":   ctx.Output,
				"costTime": ctx.End.Sub(ctx.Start).Milliseconds(),
			}))
		metrics.APICallTiming.Timing(r.Context(), ctx.End.Sub(ctx.Start).Milliseconds(),
			ctx.RouteInfo.Service, ctx.RouteInfo.Method, ctx.TenantGroup.GetName(), ctx.Customer.GetName())
		metrics.APICallCount.Incr(r.Context(), ctx.RouteInfo.Service,
			ctx.RouteInfo.Method, ctx.TenantGroup.GetName(), ctx.Customer.GetName())
	}()
	// 构建中间件链
	chain := s.buildMiddlewareChain(ctx,
		s.sentinelAPIGatewayMiddleware,
		s.routeHandler,
		s.jwtMiddleware,
		s.authMiddleware,
		s.sentinelWebMiddleware,
		s.coreHandler,
		s.responseMiddleware,
	)
	err = chain(ctx)
	if err != nil {
		// 统一错误处理 (确保 renderError 能处理 ErrSentinelBlocked)
		if re := s.renderError(ctx, err); re != nil {
			log.Errorc(ctx.Request.Context(), "renderError failed:%v", re)
		}
	}
}
