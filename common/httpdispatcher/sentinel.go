package httpdispatcher

import (
	"errors"
	"fmt"

	"github.com/Danceiny/sentinel-golang/api"
	"github.com/Danceiny/sentinel-golang/core/base"
	"github.com/Danceiny/sentinel-golang/core/flow"

	"hotel/common/log"

	"hotel/common/bizerr"
)

var ErrSentinelBlocked = bizerr.RateLimitErr

const (
	ResourceNameDefault = "DEFAULT"
)

func GetTenantResourceNames(tenantID string, apiPath string) []string {
	if tenantID == "" {
		return nil
	}
	return []string{
		"tenantEntity:" + tenantID,
		"tenantEntity:" + tenantID + ":apiPath:" + apiPath,
	}
}

func GetTenantCustomerResourceNames(tenantID, customerID string, apiPath string) []string {
	if tenantID == "" || customerID == "" {
		return nil
	}
	return []string{
		"tenantEntity:" + tenantID + ":customerEntity:" + customerID,
		"tenantEntity:" + tenantID + ":customerEntity:" + customerID + ":apiPath:" + apiPath,
	}
}

// sentinelAPIGatewayMiddleware 实现 Sentinel 限流逻辑 (无需修改)
func (s *ServiceDispatcher) sentinelAPIGatewayMiddleware(next HandlerFunc) HandlerFunc {
	return func(c *Context) error {
		resourceName := c.Request.URL.Path
		if !flow.HasRule(resourceName) {
			resourceName = ResourceNameDefault
		}
		entry, err := api.Entry(resourceName, api.WithResourceType(base.ResTypeAPIGateway), api.WithTrafficType(base.Inbound))
		if err != nil {
			var blockError *base.BlockError
			if errors.As(err, &blockError) {
				log.Warnc(c.Request.Context(), "Sentinel blocked request for resource: %s, type: %s", resourceName, blockError.BlockType().String())
				return ErrSentinelBlocked
			} else {
				log.Errorc(c.Request.Context(), "Sentinel entry error: %v", err)
				return fmt.Errorf("sentinel internal error: %w", err)
			}
		}
		defer entry.Exit()

		return next(c)
	}
}

// sentinelWebMiddleware 原有的 Sentinel Web 中间件，保持流控功能
func (s *ServiceDispatcher) sentinelWebMiddleware(next HandlerFunc) HandlerFunc {
	return func(c *Context) error {
		if c.UserInfo == nil {
			return next(c)
		}

		var (
			flowResourceNames []string
		)
		rl := c.UserInfo.LoginUser.GetTenantEntityUserLink().RateLimit
		if rl != nil {
			flowResourceNames = append(flowResourceNames, rl.Resource)
		}
		tenantEntityId := c.UserInfo.LoginUser.GetTenantBrandEntityOrAbove().GetIDStr()
		customerEntityId := c.UserInfo.LoginUser.GetCustomerEntityOrPlatform().GetIDStr()

		// 流控资源名称，即便有单独的配置这里仍然生效，确保资源兜底保护效果
		flowResourceNames = append(flowResourceNames,
			GetTenantResourceNames(tenantEntityId, c.Request.URL.Path)...,
		)
		flowResourceNames = append(flowResourceNames,
			GetTenantCustomerResourceNames(tenantEntityId, customerEntityId, c.Request.URL.Path)...)

		// 检查流控限制
		var flowEntries []*base.SentinelEntry
		for _, resourceName := range flowResourceNames {
			if !flow.HasRule(resourceName) {
				continue
			}
			entry, err := api.Entry(resourceName, api.WithResourceType(base.ResTypeWeb), api.WithTrafficType(base.Inbound))
			if err != nil {
				// 释放已获取的流控资源
				for _, e := range flowEntries {
					e.Exit()
				}

				var blockError *base.BlockError
				if errors.As(err, &blockError) {
					log.Warnc(c.Request.Context(), "Sentinel blocked request for resource: %s, type: %s", resourceName, blockError.BlockType().String())
					s.addQuotaHeaders(c, resourceName, true)
					return ErrSentinelBlocked
				} else {
					log.Errorc(c.Request.Context(), "Sentinel entry error: %v", err)
					return fmt.Errorf("sentinel internal error: %w", err)
				}
			}
			flowEntries = append(flowEntries, entry)
		}

		// 确保在函数退出时释放所有资源
		defer func() {
			for _, entry := range flowEntries {
				entry.Exit()
			}
		}()

		return next(c)
	}
}

// addQuotaHeaders 添加配额相关的响应头信息
func (s *ServiceDispatcher) addQuotaHeaders(c *Context, resourceName string, isBlocked bool) {
	rules := flow.GetRulesOfResource(resourceName)
	for _, rule := range rules {
		if rule.Resource == resourceName {
			// 添加配额限制信息
			c.Response.Header().Set("X-Quota-Limit", fmt.Sprintf("%.0f", rule.Threshold))
			c.Response.Header().Set("X-Quota-Window", fmt.Sprintf("%d", rule.StatIntervalInMs/1000))
			c.Response.Header().Set("X-Quota-Resource", resourceName)

			if isBlocked {
				c.Response.Header().Set("X-Quota-Blocked", "true")
				c.Response.Header().Set("X-Quota-Retry-After", fmt.Sprintf("%d", rule.StatIntervalInMs/1000))
			} else {
				c.Response.Header().Set("X-Quota-Blocked", "false")
			}
			break
		}
	}
}
