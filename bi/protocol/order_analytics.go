package protocol

import (
	"hotel/common/i18n"
	"hotel/common/money"
	"time"
)

// OrderAnalyticsReq represents the request structure for order analytics operation with 5 fields
type OrderAnalyticsReq struct {
	StartDate    *time.Time `json:"startDate,omitempty"`    // StartDate represents the start date for this analytics
	EndDate      *time.Time `json:"endDate,omitempty"`      // EndDate represents the end date for this analytics
	Granularity  string     `json:"granularity,omitempty"`  // Granularity contains the granularity value for this analytics
	StatusFilter []int64    `json:"statusFilter,omitempty"` // StatusFilter contains a list of status filter items
	EntityId     *int64     `json:"entityId,omitempty"`     // EntityId represents the entity identifier as an integer for this analytics
}

// OrderAnalyticsResp represents the response structure for order analytics operation with 8 fields
type OrderAnalyticsResp struct {
	Overview          *OrderOverview     `json:"overview"`          // Overview contains the overview data for this analytics
	TrendData         []*OrderTrendData  `json:"trendData"`         // TrendData contains a list of trend data items
	StatusBreakdown   []*OrderStatusStat `json:"statusBreakdown"`   // StatusBreakdown contains a list of status breakdown items
	RevenueAnalysis   *RevenueAnalysis   `json:"revenueAnalysis"`   // RevenueAnalysis contains the revenue analysis data
	TopPerformers     *TopPerformers     `json:"topPerformers"`     // TopPerformers contains the top performers data
	RegionalAnalysis  []*RegionalStat    `json:"regionalAnalysis"`  // RegionalAnalysis contains a list of regional analysis items
	SmartBookOverview *SmartBookOverview `json:"smartBookOverview"` // SmartBookOverview contains the SmartBook overview data
	RebookOverview    *RebookOverview    `json:"rebookOverview"`    // RebookOverview contains the rebook overview data
}

// SmartBookOverview SmartBook分析总览 (基于BIOrder.Tags)
type SmartBookOverview struct {
	ProcessedBookingsCount      int64       `json:"processedBookingsCount"`      // 处理的预订数量
	ProcessedBookingsAmount     money.Money `json:"processedBookingsAmount"`     // 处理的预订金额
	SmartBookProfitGenCount     int64       `json:"smartBookProfitGenCount"`     // SmartBook利润生成数量
	OptimizedProfitGenAmount    money.Money `json:"optimizedProfitGenAmount"`    // 优化的利润生成金额
	SmartBookErrorRecoveryCount int64       `json:"smartBookErrorRecoveryCount"` // SmartBook错误恢复数量
	SavedErrorRecoveryAmount    money.Money `json:"savedErrorRecoveryAmount"`    // 节省的错误恢复金额
}

// RebookOverview Rebook分析总览
type RebookOverview struct {
	ProcessedBookingsCount   int64              `json:"processedBookingsCount"`   // 处理的重预订数量
	ProcessedBookingsAmount  money.Money        `json:"processedBookingsAmount"`  // 处理的重预订金额
	RebookProfitGenCount     int64              `json:"rebookProfitGenCount"`     // Rebook利润生成数量
	OptimizedProfitGenAmount money.Money        `json:"optimizedProfitGenAmount"` // 优化的利润生成金额
	SupplierTransfers        []SupplierTransfer `json:"supplierTransfers"`        // 供应商转换记录
}

// SupplierTransfer 供应商转换记录
type SupplierTransfer struct {
	OrderID      int64       `json:"orderID"`      // 订单ID
	OldSupplier  string      `json:"oldSupplier"`  // 原供应商
	NewSupplier  string      `json:"newSupplier"`  // 新供应商
	Amount       money.Money `json:"amount"`       // 转换金额
	TransferTime time.Time   `json:"transferTime"` // 转换时间
	Reason       string      `json:"reason"`       // 转换原因
}

// ClientAnalytics 客户分析 (基于"Know your clients"需求)
type ClientAnalytics struct {
	EntityID         int64                 `json:"entityID"`         // 实体ID
	Configuration    ClientConfiguration   `json:"configuration"`    // 客户配置分析
	RequestAnalysis  RequestAnalysis       `json:"requestAnalysis"`  // 请求分析
	ResponseAnalysis ResponseAnalysis      `json:"responseAnalysis"` // 响应分析
	SupplierBookings []SupplierBookingStat `json:"supplierBookings"` // 供应商预订统计
}

// ClientConfiguration 客户配置分析
type ClientConfiguration struct {
	RoomMappingVersion string `json:"roomMappingVersion"` // 房型映射版本
	SmartBookMode      string `json:"smartBookMode"`      // SmartBook模式
	DynamicMarkups     bool   `json:"dynamicMarkups"`     // 动态加价是否启用
	BestPackages       bool   `json:"bestPackages"`       // 最佳套餐是否启用
	TwoFactorAuth      bool   `json:"twoFactorAuth"`      // 双因素认证是否启用
}

// RequestAnalysis 请求分析
type RequestAnalysis struct {
	SearchTypeCount   map[string]int64 `json:"searchTypeCount"`   // 搜索类型计数(geo,...)
	ListSearchCount   int64            `json:"listSearchCount"`   // 列表搜索计数
	DetailSearchCount int64            `json:"detailSearchCount"` // 详情搜索计数
	CheckAvailCount   int64            `json:"checkAvailCount"`   // 可用性检查计数
}

// ResponseAnalysis 响应分析
type ResponseAnalysis struct {
	SuccessRate float64 `json:"successRate"` // 成功率
	FailureRate float64 `json:"failureRate"` // 失败率
	AvgDuration float64 `json:"avgDuration"` // 平均响应时间
	P50Duration float64 `json:"p50Duration"` // P50响应时间
	P95Duration float64 `json:"p95Duration"` // P95响应时间
}

// SupplierBookingStat 供应商预订统计
type SupplierBookingStat struct {
	Supplier      string      `json:"supplier"`      // 供应商
	BookingCount  int64       `json:"bookingCount"`  // 预订数量
	BookingVolume money.Money `json:"bookingVolume"` // 预订金额
}

// OrderOverview 订单总览
type OrderOverview struct {
	TotalOrders       int64       `json:"totalOrders"`       // 总订单数
	TotalRevenue      money.Money `json:"totalRevenue"`      // 总收入
	TotalProfit       money.Money `json:"totalProfit"`       // 总利润
	AverageOrderValue money.Money `json:"averageOrderValue"` // 平均订单价值
	BookingRate       float64     `json:"bookingRate"`       // 预订转化率
	CancellationRate  float64     `json:"cancellationRate"`  // 取消率
	CompletionRate    float64     `json:"completionRate"`    // 完成率
	GrowthRate        float64     `json:"growthRate"`        // 增长率(相比上期)
}

// OrderTrendData 订单趋势数据
type OrderTrendData struct {
	Date         time.Time   `json:"date"`         // 日期
	OrderCount   int64       `json:"orderCount"`   // 订单数量
	Revenue      money.Money `json:"revenue"`      // 收入
	Profit       money.Money `json:"profit"`       // 利润
	BookingCount int64       `json:"bookingCount"` // 预订数量
	CancelCount  int64       `json:"cancelCount"`  // 取消数量
}

// OrderStatusStat 订单状态统计
type OrderStatusStat struct {
	Status      int64          `json:"status"`      // 状态码
	StatusName  i18n.I18N      `json:"statusName"`  // 状态名称
	Count       int64          `json:"count"`       // 数量
	Percentage  float64        `json:"percentage"`  // 占比
	Revenue     money.Money    `json:"revenue"`     // 该状态订单总收入
	AvgDuration *time.Duration `json:"avgDuration"` // 平均持续时间
}

// RevenueAnalysis 收入分析
type RevenueAnalysis struct {
	TotalRevenue      money.Money          `json:"totalRevenue"`      // 总收入
	TotalCost         money.Money          `json:"totalCost"`         // 总成本
	TotalProfit       money.Money          `json:"totalProfit"`       // 总利润
	ProfitMargin      float64              `json:"profitMargin"`      // 利润率
	RevenueByCurrency []*CurrencyRevenue   `json:"revenueByCurrency"` // 按币种收入
	RevenueByEntity   []*EntityRevenue     `json:"revenueByEntity"`   // 按实体收入
	MonthlyComparison []*MonthlyComparison `json:"monthlyComparison"` // 月度对比
}

// CurrencyRevenue 币种收入
type CurrencyRevenue struct {
	Currency   string      `json:"currency"`   // 币种
	Revenue    money.Money `json:"revenue"`    // 收入
	OrderCount int64       `json:"orderCount"` // 订单数
	Percentage float64     `json:"percentage"` // 占比
}

// EntityRevenue 实体收入
type EntityRevenue struct {
	EntityId   int64       `json:"entityId"`   // 实体ID
	EntityName i18n.I18N   `json:"entityName"` // 实体名称
	Revenue    money.Money `json:"revenue"`    // 收入
	Profit     money.Money `json:"profit"`     // 利润
	OrderCount int64       `json:"orderCount"` // 订单数
	GrowthRate float64     `json:"growthRate"` // 增长率
}

// MonthlyComparison 月度对比
type MonthlyComparison struct {
	Month      time.Time   `json:"month"`      // 月份
	Revenue    money.Money `json:"revenue"`    // 收入
	Profit     money.Money `json:"profit"`     // 利润
	OrderCount int64       `json:"orderCount"` // 订单数
	GrowthRate float64     `json:"growthRate"` // 增长率
}

// TopPerformers 表现排行
type TopPerformers struct {
	TopCustomers []*CustomerPerformance `json:"topCustomers"` // 顶级客户
	TopEntities  []*EntityPerformance   `json:"topEntities"`  // 顶级实体
	TopDays      []*DayPerformance      `json:"topDays"`      // 表现最佳日期
}

// CustomerPerformance 客户表现
type CustomerPerformance struct {
	CustomerId    int64       `json:"customerId"`    // 客户ID
	CustomerName  i18n.I18N   `json:"customerName"`  // 客户名称
	TotalRevenue  money.Money `json:"totalRevenue"`  // 总收入
	OrderCount    int64       `json:"orderCount"`    // 订单数
	AvgOrderValue money.Money `json:"avgOrderValue"` // 平均订单价值
	LastOrderDate time.Time   `json:"lastOrderDate"` // 最后订单日期
}

// EntityPerformance 实体表现
type EntityPerformance struct {
	EntityId     int64       `json:"entityId"`     // 实体ID
	EntityName   i18n.I18N   `json:"entityName"`   // 实体名称
	TotalRevenue money.Money `json:"totalRevenue"` // 总收入
	TotalProfit  money.Money `json:"totalProfit"`  // 总利润
	OrderCount   int64       `json:"orderCount"`   // 订单数
	ProfitMargin float64     `json:"profitMargin"` // 利润率
}

// DayPerformance 日期表现
type DayPerformance struct {
	Date       time.Time   `json:"date"`       // 日期
	Revenue    money.Money `json:"revenue"`    // 收入
	OrderCount int64       `json:"orderCount"` // 订单数
	Profit     money.Money `json:"profit"`     // 利润
}

// RegionalStat 区域统计
type RegionalStat struct {
	Region     string      `json:"region"`     // 区域
	OrderCount int64       `json:"orderCount"` // 订单数
	Revenue    money.Money `json:"revenue"`    // 收入
	Profit     money.Money `json:"profit"`     // 利润
	GrowthRate float64     `json:"growthRate"` // 增长率
}

// OrderMetricsReq 订单指标请求
type OrderMetricsReq struct {
	MetricType  string                 `json:"metricType"` // 指标类型: overview/trend/status/revenue
	StartDate   *time.Time             `json:"startDate,omitempty"`
	EndDate     *time.Time             `json:"endDate,omitempty"`
	Granularity string                 `json:"granularity,omitempty"` // day/week/month
	Filters     map[string]interface{} `json:"filters,omitempty"`     // 其他过滤条件
}

// OrderMetricsResp 订单指标响应
type OrderMetricsResp struct {
	MetricType string      `json:"metricType"` // 指标类型
	Data       interface{} `json:"data"`       // 具体数据，根据MetricType动态决定结构
	UpdateTime time.Time   `json:"updateTime"` // 数据更新时间
}

// RealTimeMetricsReq 实时指标请求
type RealTimeMetricsReq struct {
	Metrics []string `json:"metrics"` // 需要的指标列表
}

// RealTimeMetricsResp 实时指标响应
type RealTimeMetricsResp struct {
	TodayOrders      int64       `json:"todayOrders"`      // 今日订单
	TodayRevenue     money.Money `json:"todayRevenue"`     // 今日收入
	ActiveBookings   int64       `json:"activeBookings"`   // 活跃预订
	PendingOrders    int64       `json:"pendingOrders"`    // 待处理订单
	CompletionRate   float64     `json:"completionRate"`   // 完成率
	CancellationRate float64     `json:"cancellationRate"` // 取消率
	UpdateTime       time.Time   `json:"updateTime"`       // 更新时间
}

// ExportReq 导出请求
type ExportReq struct {
	ExportType string                 `json:"exportType"` // 导出类型: excel/csv/pdf
	DataType   string                 `json:"dataType"`   // 数据类型: orders/analytics/revenue
	StartDate  *time.Time             `json:"startDate,omitempty"`
	EndDate    *time.Time             `json:"endDate,omitempty"`
	Filters    map[string]interface{} `json:"filters,omitempty"`
	Fields     []string               `json:"fields,omitempty"` // 需要导出的字段
}

// ExportResp 导出响应
type ExportResp struct {
	FileUrl    string    `json:"fileUrl"`    // 文件下载链接
	FileName   string    `json:"fileName"`   // 文件名
	FileSize   int64     `json:"fileSize"`   // 文件大小
	ExpiryTime time.Time `json:"expiryTime"` // 过期时间
}
