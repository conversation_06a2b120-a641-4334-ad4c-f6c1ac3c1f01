<template>
  <div class="notifications-container">
    <!-- 通知发送区域 -->
    <el-card class="send-notification-card">
      <template #header>
        <div class="card-header">
          <span>发送通知</span>
          <el-button type="primary" @click="showSendDialog = true">
            发送通知
          </el-button>
        </div>
      </template>
      
      <div class="quick-stats">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.totalSent || 0 }}</div>
              <div class="stat-label">总发送数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.successRate || 0 }}%</div>
              <div class="stat-label">成功率</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.todaySent || 0 }}</div>
              <div class="stat-label">今日发送</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ stats.templates || 0 }}</div>
              <div class="stat-label">模板数量</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 模板管理区域 -->
    <el-card class="template-management-card">
      <template #header>
        <div class="card-header">
          <span>通知模板</span>
          <el-button type="primary" @click="showTemplateDialog = true">
            创建模板
          </el-button>
        </div>
      </template>
      
      <el-table :data="templates" :loading="loading" stripe>
        <el-table-column prop="name" label="模板名称" width="150" />
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTag(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scenario" label="场景" width="150" />
        <el-table-column prop="description" label="描述" min-width="200" />
        <el-table-column prop="createdAt" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="editTemplate(row)">
              编辑
            </el-button>
            <el-button type="danger" size="small" @click="deleteTemplate(row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="templatePagination.currentPage"
          v-model:page-size="templatePagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="templatePagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleTemplateSizeChange"
          @current-change="handleTemplateCurrentChange"
        />
      </div>
    </el-card>

    <!-- 发送历史区域 -->
    <el-card class="history-card">
      <template #header>
        <div class="card-header">
          <span>发送历史</span>
          <div>
            <el-button @click="refreshHistory">刷新</el-button>
            <el-button type="success" @click="exportHistory">导出</el-button>
          </div>
        </div>
      </template>
      
      <el-table :data="histories" :loading="historyLoading" stripe>
        <el-table-column prop="type" label="类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTag(row.type)">
              {{ getTypeLabel(row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="scenario" label="场景" width="150" />
        <el-table-column prop="recipient" label="接收者" width="200" />
        <el-table-column prop="subject" label="主题" min-width="200" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTag(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sentAt" label="发送时间" width="180">
          <template #default="{ row }">
            {{ formatTime(row.sentAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="viewHistoryDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="historyPagination.currentPage"
          v-model:page-size="historyPagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="historyPagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleHistorySizeChange"
          @current-change="handleHistoryCurrentChange"
        />
      </div>
    </el-card>

    <!-- 发送通知弹窗 -->
    <SendNotificationDialog
      v-model:visible="showSendDialog"
      :templates="templates"
      @send-success="handleSendSuccess"
    />

    <!-- 模板编辑弹窗 -->
    <TemplateDialog
      v-model:visible="showTemplateDialog"
      :template="editingTemplate"
      @save-success="handleTemplateSave"
    />

    <!-- 历史详情弹窗 -->
    <HistoryDetailDialog
      v-model:visible="showHistoryDetail"
      :history="selectedHistory"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import SendNotificationDialog from './components/SendNotificationDialog.vue'
import TemplateDialog from './components/TemplateDialog.vue'
import HistoryDetailDialog from './components/HistoryDetailDialog.vue'
import { notificationApi } from '@/api/notification'
import type { TemplateItem, NotificationHistoryItem } from '@/api/notification/types'

// 响应式数据
const loading = ref(false)
const historyLoading = ref(false)
const templates = ref<TemplateItem[]>([])
const histories = ref<NotificationHistoryItem[]>([])
const showSendDialog = ref(false)
const showTemplateDialog = ref(false)
const showHistoryDetail = ref(false)
const editingTemplate = ref<TemplateItem | null>(null)
const selectedHistory = ref<NotificationHistoryItem | null>(null)

// 统计数据
const stats = reactive({
  totalSent: 0,
  successRate: 0,
  todaySent: 0,
  templates: 0
})

// 分页信息
const templatePagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

const historyPagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 生命周期
onMounted(() => {
  loadTemplates()
  loadHistories()
  loadStats()
})

// 加载模板列表
const loadTemplates = async () => {
  try {
    loading.value = true
    const response = await notificationApi.listTemplates({
      page: templatePagination.currentPage,
      pageSize: templatePagination.pageSize
    })
    templates.value = response.templates
    templatePagination.total = response.total
  } catch (error) {
    console.error('加载模板失败:', error)
    ElMessage.error('加载模板失败')
  } finally {
    loading.value = false
  }
}

// 加载历史记录
const loadHistories = async () => {
  try {
    historyLoading.value = true
    const response = await notificationApi.getHistory({
      page: historyPagination.currentPage,
      pageSize: historyPagination.pageSize
    })
    histories.value = response.histories
    historyPagination.total = response.total
  } catch (error) {
    console.error('加载历史记录失败:', error)
    ElMessage.error('加载历史记录失败')
  } finally {
    historyLoading.value = false
  }
}

// 加载统计数据
const loadStats = async () => {
  try {
    // 这里可以调用统计接口
    // const response = await notificationApi.getStats()
    // Object.assign(stats, response)
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

// 事件处理
const handleSendSuccess = () => {
  ElMessage.success('通知发送成功')
  loadHistories()
  loadStats()
}

const handleTemplateSave = () => {
  ElMessage.success('模板保存成功')
  loadTemplates()
}

const editTemplate = (template: TemplateItem) => {
  editingTemplate.value = template
  showTemplateDialog.value = true
}

const deleteTemplate = async (template: TemplateItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板 "${template.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    await notificationApi.deleteTemplate(template.id)
    ElMessage.success('删除成功')
    loadTemplates()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error)
      ElMessage.error('删除模板失败')
    }
  }
}

const refreshHistory = () => {
  loadHistories()
}

const exportHistory = () => {
  ElMessage.info('导出功能开发中')
}

const viewHistoryDetail = (history: NotificationHistoryItem) => {
  selectedHistory.value = history
  showHistoryDetail.value = true
}

// 分页处理
const handleTemplateSizeChange = (size: number) => {
  templatePagination.pageSize = size
  loadTemplates()
}

const handleTemplateCurrentChange = (page: number) => {
  templatePagination.currentPage = page
  loadTemplates()
}

const handleHistorySizeChange = (size: number) => {
  historyPagination.pageSize = size
  loadHistories()
}

const handleHistoryCurrentChange = (page: number) => {
  historyPagination.currentPage = page
  loadHistories()
}

// 工具方法
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    email: '邮件',
    sms: '短信',
    push: '推送'
  }
  return typeMap[type] || type
}

const getTypeTag = (type: string) => {
  const tagMap: Record<string, string> = {
    email: 'success',
    sms: 'warning',
    push: 'info'
  }
  return tagMap[type] || 'info'
}

const getStatusLabel = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待发送',
    sent: '已发送',
    failed: '发送失败'
  }
  return statusMap[status] || status
}

const getStatusTag = (status: string) => {
  const tagMap: Record<string, string> = {
    pending: 'info',
    sent: 'success',
    failed: 'danger'
  }
  return tagMap[status] || 'info'
}
</script>

<style scoped>
.notifications-container {
  padding: 20px;
}

.send-notification-card,
.template-management-card,
.history-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quick-stats {
  padding: 20px 0;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-tag) {
  font-size: 12px;
}
</style> 