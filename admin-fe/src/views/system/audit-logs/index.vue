<template>
  <div class="audit-logs-viewer">
    <!-- 统计概览区域 -->
    <div class="statistics-section" v-if="showStatistics">
      <AuditLogStatistics :statistics="statistics" :loading="loading" />
    </div>

    <!-- 搜索过滤区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <template #header>
          <div class="card-header">
            <span>审计日志查询</span>
            <div>
              <el-button @click="toggleStatistics">
                {{ showStatistics ? '隐藏统计' : '显示统计' }}
              </el-button>
              <el-button type="primary" @click="toggleAdvancedSearch">
                {{ showAdvancedSearch ? '简单搜索' : '高级搜索' }}
              </el-button>
            </div>
          </div>
        </template>
        
        <!-- 基础搜索 -->
        <el-form :model="searchForm" :inline="true" class="search-form">
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="timeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ss.SSSZ"
              @change="handleTimeRangeChange"
            />
          </el-form-item>
          
          <el-form-item label="操作类型">
            <el-select
              v-model="searchForm.actionType"
              placeholder="请选择操作类型"
              clearable
            >
              <el-option label="用户邀请" value="USER_INVITE" />
              <el-option label="角色分配" value="ROLE_ASSIGN" />
              <el-option label="停用账户" value="DEACTIVATION" />
              <el-option label="身份冒充" value="IMPERSONATION" />
              <el-option label="登录" value="LOGIN" />
              <el-option label="登出" value="LOGOUT" />
              <el-option label="密码重置" value="PASSWORD_RESET" />
              <el-option label="实体创建" value="ENTITY_CREATE" />
              <el-option label="实体更新" value="ENTITY_UPDATE" />
              <el-option label="实体删除" value="ENTITY_DELETE" />
            </el-select>
          </el-form-item>
          
          <el-form-item label="关键词">
            <el-input
              v-model="searchForm.keyword"
              placeholder="请输入关键词搜索"
              clearable
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleExport">导出</el-button>
          </el-form-item>
        </el-form>
        
        <!-- 高级搜索 -->
        <div v-show="showAdvancedSearch" class="advanced-search">
          <el-divider content-position="left">高级搜索</el-divider>
          <el-form :model="searchForm" label-width="120px">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="操作者ID">
                  <el-input v-model="searchForm.actorUserId" placeholder="请输入操作者ID" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="受影响实体ID">
                  <el-input v-model="searchForm.affectedEntityId" placeholder="请输入受影响实体ID" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="排序字段">
                  <el-select v-model="searchForm.sortBy" placeholder="请选择排序字段">
                    <el-option label="操作时间" value="actionTime" />
                    <el-option label="操作类型" value="actionType" />
                    <el-option label="操作者ID" value="actorUserId" />
                    <el-option label="受影响实体ID" value="affectedEntityId" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="排序方式">
                  <el-select v-model="searchForm.sortOrder" placeholder="请选择排序方式">
                    <el-option label="升序" value="asc" />
                    <el-option label="降序" value="desc" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="包含详情">
                  <el-switch v-model="searchForm.includeDetails" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-card>
    </div>

    <!-- 日志列表区域 -->
    <div class="logs-section">
      <el-card class="logs-card">
        <template #header>
          <div class="card-header">
            <span>审计日志列表</span>
            <div>
              <el-button @click="handleRefresh">刷新</el-button>
              <el-button @click="handleAutoRefresh" :type="autoRefresh ? 'success' : ''">
                {{ autoRefresh ? '停止自动刷新' : '自动刷新' }}
              </el-button>
            </div>
          </div>
        </template>
        
        <el-table
          :data="auditLogs"
          :loading="loading"
          stripe
          @row-click="handleRowClick"
          class="audit-logs-table"
        >
          <el-table-column prop="actionType" label="操作类型" width="120">
            <template #default="{ row }">
              <el-tag :type="getActionTypeTag(row.actionType)">
                {{ getActionTypeLabel(row.actionType) }}
              </el-tag>
            </template>
          </el-table-column>
          
          <el-table-column prop="actionTime" label="操作时间" width="180">
            <template #default="{ row }">
              {{ formatTime(row.actionTime) }}
            </template>
          </el-table-column>
          
          <el-table-column prop="actorUserId" label="操作者ID" width="100" />
          
          <el-table-column prop="actorInfo" label="操作者信息" width="200">
            <template #default="{ row }">
              <el-tooltip :content="row.actorInfo" placement="top">
                <span>{{ truncateText(row.actorInfo, 30) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          
          <el-table-column prop="affectedEntityId" label="受影响实体ID" width="120" />
          
          <el-table-column prop="affectedEntityInfo" label="受影响实体信息" width="200">
            <template #default="{ row }">
              <el-tooltip :content="row.affectedEntityInfo" placement="top">
                <span>{{ truncateText(row.affectedEntityInfo, 30) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          
          <el-table-column prop="details" label="操作详情" min-width="200">
            <template #default="{ row }">
              <el-tooltip :content="formatDetails(row.details)" placement="top">
                <span>{{ truncateText(formatDetails(row.details), 50) }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          
          <el-table-column label="操作" width="100" fixed="right">
            <template #default="{ row }">
              <el-button type="primary" size="small" @click="viewDetail(row)">
                详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        
        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>
    
    <!-- 日志详情弹窗 -->
    <AuditLogDetailDialog
      v-model:visible="detailDialogVisible"
      :log-data="selectedLog"
      @refresh="handleRefresh"
    />
    
    <!-- 导出弹窗 -->
    <ExportDialog
      v-model:visible="exportDialogVisible"
      :search-params="searchForm"
      @export-success="handleExportSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import AuditLogDetailDialog from './components/AuditLogDetailDialog.vue'
import ExportDialog from './components/ExportDialog.vue'
import AuditLogStatistics from './components/AuditLogStatistics.vue'
import { auditLogApi } from '@/api/auditLog'
import type { AuditLogItem, QueryAuditLogsParams } from '@/api/auditLog/types'

// 响应式数据
const loading = ref(false)
const auditLogs = ref<AuditLogItem[]>([])
const statistics = ref<any>(null)
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)
const showAdvancedSearch = ref(false)
const showStatistics = ref(false)
const autoRefresh = ref(false)
const detailDialogVisible = ref(false)
const exportDialogVisible = ref(false)
const selectedLog = ref<AuditLogItem | null>(null)

// 时间范围
const timeRange = ref<[string, string]>(['', ''])

// 搜索表单
const searchForm = reactive<QueryAuditLogsParams>({
  page: 1,
  pageSize: 20,
  actionType: '',
  keyword: '',
  actorUserId: undefined,
  affectedEntityId: undefined,
  includeDetails: false,
  sortBy: 'actionTime',
  sortOrder: 'desc'
})

// 分页信息
const pagination = reactive({
  currentPage: 1,
  pageSize: 20,
  total: 0
})

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | null = null

// 生命周期
onMounted(() => {
  loadAuditLogs()
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})

// 监听自动刷新
watch(autoRefresh, (newVal) => {
  if (newVal) {
    refreshTimer = setInterval(() => {
      loadAuditLogs()
    }, 30000) // 30秒自动刷新
  } else if (refreshTimer) {
    clearInterval(refreshTimer)
    refreshTimer = null
  }
})

// 事件处理
const handleTimeRangeChange = (range: [string, string]) => {
  timeRange.value = range
}

const handleSearch = () => {
  currentPage.value = 1
  loadAuditLogs()
}

const handleReset = () => {
  Object.assign(searchForm, {
    actionType: '',
    keyword: '',
    actorUserId: undefined,
    affectedEntityId: undefined,
    includeDetails: false,
    sortBy: 'actionTime',
    sortOrder: 'desc'
  })
  timeRange.value = ['', '']
  currentPage.value = 1
  loadAuditLogs()
}

const handleRefresh = () => {
  loadAuditLogs()
}

const handleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
}

const handleRowClick = (row: AuditLogItem) => {
  viewDetail(row)
}

const viewDetail = (row: AuditLogItem) => {
  selectedLog.value = row
  detailDialogVisible.value = true
}

const handleExport = () => {
  exportDialogVisible.value = true
}

const handleExportSuccess = () => {
  ElMessage.success('导出成功')
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  searchForm.pageSize = size
  loadAuditLogs()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  searchForm.page = page
  loadAuditLogs()
}

const toggleStatistics = () => {
  showStatistics.value = !showStatistics.value
}

const toggleAdvancedSearch = () => {
  showAdvancedSearch.value = !showAdvancedSearch.value
}

// 加载审计日志数据
const loadAuditLogs = async () => {
  try {
    loading.value = true
    
    // 构建查询参数
    const params = { ...searchForm }
    
    // 处理时间范围
    if (timeRange.value[0] && timeRange.value[1]) {
      params.actionTime = {
        start: timeRange.value[0],
        end: timeRange.value[1]
      }
    }
    
    const response = await auditLogApi.queryAuditLogs(params)
    auditLogs.value = response.table.rows || []
    total.value = response.table.total || 0
    statistics.value = response.statistics
    
    // 更新分页信息
    pagination.total = total.value
    pagination.currentPage = currentPage.value
    pagination.pageSize = pageSize.value
  } catch (error) {
    console.error('加载审计日志失败:', error)
    ElMessage.error('加载审计日志失败')
  } finally {
    loading.value = false
  }
}

// 工具方法
const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const truncateText = (text: string, maxLength: number) => {
  if (!text) return '-'
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

const formatDetails = (details: any) => {
  if (!details) return '-'
  if (typeof details === 'string') return details
  return JSON.stringify(details, null, 2)
}

const getActionTypeLabel = (actionType: string) => {
  const actionTypeMap: Record<string, string> = {
    'USER_INVITE': '用户邀请',
    'ROLE_ASSIGN': '角色分配',
    'DEACTIVATION': '停用账户',
    'IMPERSONATION': '身份冒充',
    'LOGIN': '登录',
    'LOGOUT': '登出',
    'PASSWORD_RESET': '密码重置',
    'ENTITY_CREATE': '实体创建',
    'ENTITY_UPDATE': '实体更新',
    'ENTITY_DELETE': '实体删除'
  }
  return actionTypeMap[actionType] || actionType
}

const getActionTypeTag = (actionType: string) => {
  const tagMap: Record<string, string> = {
    'USER_INVITE': 'success',
    'ROLE_ASSIGN': 'warning',
    'DEACTIVATION': 'danger',
    'IMPERSONATION': 'danger',
    'LOGIN': 'info',
    'LOGOUT': 'info',
    'PASSWORD_RESET': 'warning',
    'ENTITY_CREATE': 'success',
    'ENTITY_UPDATE': 'warning',
    'ENTITY_DELETE': 'danger'
  }
  return tagMap[actionType] || 'info'
}
</script>

<style scoped>
.audit-logs-viewer {
  padding: 20px;
}

.statistics-section {
  margin-bottom: 20px;
}

.search-section {
  margin-bottom: 20px;
}

.search-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  margin-bottom: 20px;
}

.advanced-search {
  margin-top: 20px;
}

.logs-section {
  margin-bottom: 20px;
}

.logs-card {
  margin-bottom: 20px;
}

.audit-logs-table {
  margin-bottom: 20px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 500;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-tag) {
  font-size: 12px;
}
</style> 