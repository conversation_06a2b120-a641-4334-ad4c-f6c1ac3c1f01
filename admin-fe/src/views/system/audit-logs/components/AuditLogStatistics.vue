<template>
  <el-card class="statistics-card">
    <template #header>
      <div class="card-header">
        <span>审计日志统计</span>
        <el-button @click="refreshStatistics" :loading="loading">
          刷新统计
        </el-button>
      </div>
    </template>

    <div v-if="statistics" class="statistics-content">
      <!-- 总览数据 -->
      <el-row :gutter="20" class="overview-section">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.totalCount }}</div>
              <div class="stat-label">总日志数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ Object.keys(statistics.actionTypeCount).length }}</div>
              <div class="stat-label">操作类型数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.topActors.length }}</div>
              <div class="stat-label">活跃操作者</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-item">
              <div class="stat-value">{{ statistics.topEntities.length }}</div>
              <div class="stat-label">活跃实体</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 操作类型分布 -->
      <el-row :gutter="20" class="chart-section">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>操作类型分布</span>
            </template>
            <div class="chart-container">
              <div
                v-for="(count, type) in statistics.actionTypeCount"
                :key="type"
                class="type-item"
              >
                <div class="type-label">{{ getActionTypeLabel(type) }}</div>
                <div class="type-bar">
                  <div
                    class="type-progress"
                    :style="{ width: getPercentage(count, statistics.totalCount) + '%' }"
                  ></div>
                </div>
                <div class="type-count">{{ count }}</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 最近活动 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>最近活动</span>
            </template>
            <div class="recent-activities">
              <div
                v-for="activity in statistics.recentActivities.slice(0, 10)"
                :key="`${activity.actionType}-${activity.actorUserId}-${activity.actionTime}`"
                class="activity-item"
              >
                <div class="activity-time">{{ formatTime(activity.actionTime) }}</div>
                <div class="activity-content">
                  <el-tag :type="getActionTypeTag(activity.actionType)" size="small">
                    {{ getActionTypeLabel(activity.actionType) }}
                  </el-tag>
                  <span class="activity-user">用户 {{ activity.actorUserId }}</span>
                  <span class="activity-entity">实体 {{ activity.affectedEntityId }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 活跃操作者和实体 -->
      <el-row :gutter="20" class="chart-section">
        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>活跃操作者</span>
            </template>
            <div class="top-list">
              <div
                v-for="(actor, index) in statistics.topActors.slice(0, 10)"
                :key="actor.actorUserId"
                class="top-item"
              >
                <div class="rank">{{ index + 1 }}</div>
                <div class="user-info">
                  <div class="user-id">用户 {{ actor.actorUserId }}</div>
                  <div class="last-action">{{ formatTime(actor.lastAction) }}</div>
                </div>
                <div class="action-count">{{ actor.actionCount }} 次</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="chart-card">
            <template #header>
              <span>活跃实体</span>
            </template>
            <div class="top-list">
              <div
                v-for="(entity, index) in statistics.topEntities.slice(0, 10)"
                :key="entity.entityId"
                class="top-item"
              >
                <div class="rank">{{ index + 1 }}</div>
                <div class="entity-info">
                  <div class="entity-id">实体 {{ entity.entityId }}</div>
                  <div class="last-action">{{ formatTime(entity.lastAction) }}</div>
                </div>
                <div class="action-count">{{ entity.actionCount }} 次</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div v-else class="no-data">
      <el-empty description="暂无统计数据" />
    </div>
  </el-card>
</template>

<script setup lang="ts">
import type { AuditLogStatistics } from '@/api/auditLog/types'

interface Props {
  statistics: AuditLogStatistics | null
  loading: boolean
}

interface Emits {
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const refreshStatistics = () => {
  emit('refresh')
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getActionTypeLabel = (actionType: string) => {
  const actionTypeMap: Record<string, string> = {
    'USER_INVITE': '用户邀请',
    'ROLE_ASSIGN': '角色分配',
    'DEACTIVATION': '停用账户',
    'IMPERSONATION': '身份冒充',
    'LOGIN': '登录',
    'LOGOUT': '登出',
    'PASSWORD_RESET': '密码重置',
    'ENTITY_CREATE': '实体创建',
    'ENTITY_UPDATE': '实体更新',
    'ENTITY_DELETE': '实体删除'
  }
  return actionTypeMap[actionType] || actionType
}

const getActionTypeTag = (actionType: string) => {
  const tagMap: Record<string, string> = {
    'USER_INVITE': 'success',
    'ROLE_ASSIGN': 'warning',
    'DEACTIVATION': 'danger',
    'IMPERSONATION': 'danger',
    'LOGIN': 'info',
    'LOGOUT': 'info',
    'PASSWORD_RESET': 'warning',
    'ENTITY_CREATE': 'success',
    'ENTITY_UPDATE': 'warning',
    'ENTITY_DELETE': 'danger'
  }
  return tagMap[actionType] || 'info'
}

const getPercentage = (count: number, total: number) => {
  if (total === 0) return 0
  return Math.round((count / total) * 100)
}
</script>

<style scoped>
.statistics-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.statistics-content {
  padding: 0;
}

.overview-section {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
  border: 1px solid #e4e7ed;
}

.stat-item {
  padding: 20px;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.chart-section {
  margin-bottom: 20px;
}

.chart-card {
  height: 400px;
}

.chart-container {
  height: 320px;
  overflow-y: auto;
}

.type-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.type-label {
  width: 120px;
  font-size: 14px;
  color: #606266;
}

.type-bar {
  flex: 1;
  height: 8px;
  background-color: #e4e7ed;
  border-radius: 4px;
  margin: 0 12px;
  overflow: hidden;
}

.type-progress {
  height: 100%;
  background-color: #409eff;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.type-count {
  width: 60px;
  text-align: right;
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.recent-activities {
  height: 320px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-time {
  width: 120px;
  font-size: 12px;
  color: #909399;
}

.activity-content {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.activity-user,
.activity-entity {
  font-size: 12px;
  color: #606266;
}

.top-list {
  height: 320px;
  overflow-y: auto;
}

.top-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  margin-right: 12px;
}

.user-info,
.entity-info {
  flex: 1;
}

.user-id,
.entity-id {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.last-action {
  font-size: 12px;
  color: #909399;
}

.action-count {
  font-size: 14px;
  font-weight: 500;
  color: #409eff;
}

.no-data {
  padding: 40px;
  text-align: center;
}

:deep(.el-card__header) {
  padding: 12px 20px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 20px;
}
</style> 