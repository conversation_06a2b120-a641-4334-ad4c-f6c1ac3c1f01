<template>
  <el-dialog
    v-model="dialogVisible"
    title="审计日志详情"
    width="800px"
    :before-close="handleClose"
  >
    <div v-if="logData" class="audit-log-detail">
      <!-- 基本信息 -->
      <el-descriptions title="基本信息" :column="2" border>
        <el-descriptions-item label="日志ID">
          {{ logData.id }}
        </el-descriptions-item>
        <el-descriptions-item label="操作类型">
          <el-tag :type="getActionTypeTag(logData.actionType)">
            {{ getActionTypeLabel(logData.actionType) }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="操作时间">
          {{ formatTime(logData.actionTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="操作者ID">
          {{ logData.actorUserId }}
        </el-descriptions-item>
        <el-descriptions-item label="受影响实体ID">
          {{ logData.affectedEntityId }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 操作者信息 -->
      <el-descriptions title="操作者信息" :column="1" border class="mt-4">
        <el-descriptions-item label="操作者信息">
          <pre class="json-content">{{ formatJson(logData.actorInfo) }}</pre>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 受影响实体信息 -->
      <el-descriptions title="受影响实体信息" :column="1" border class="mt-4">
        <el-descriptions-item label="实体信息">
          <pre class="json-content">{{ formatJson(logData.affectedEntityInfo) }}</pre>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 操作详情 -->
      <el-descriptions title="操作详情" :column="1" border class="mt-4">
        <el-descriptions-item label="详情">
          <pre class="json-content">{{ formatJson(logData.details) }}</pre>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleRefresh">刷新</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { AuditLogItem } from '@/api/auditLog/types'

interface Props {
  visible: boolean
  logData: AuditLogItem | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'refresh'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const handleClose = () => {
  dialogVisible.value = false
}

const handleRefresh = () => {
  emit('refresh')
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const formatJson = (data: any) => {
  if (!data) return '无数据'
  if (typeof data === 'string') {
    try {
      return JSON.stringify(JSON.parse(data), null, 2)
    } catch {
      return data
    }
  }
  return JSON.stringify(data, null, 2)
}

const getActionTypeLabel = (actionType: string) => {
  const actionTypeMap: Record<string, string> = {
    'USER_INVITE': '用户邀请',
    'ROLE_ASSIGN': '角色分配',
    'DEACTIVATION': '停用账户',
    'IMPERSONATION': '身份冒充',
    'LOGIN': '登录',
    'LOGOUT': '登出',
    'PASSWORD_RESET': '密码重置',
    'ENTITY_CREATE': '实体创建',
    'ENTITY_UPDATE': '实体更新',
    'ENTITY_DELETE': '实体删除'
  }
  return actionTypeMap[actionType] || actionType
}

const getActionTypeTag = (actionType: string) => {
  const tagMap: Record<string, string> = {
    'USER_INVITE': 'success',
    'ROLE_ASSIGN': 'warning',
    'DEACTIVATION': 'danger',
    'IMPERSONATION': 'danger',
    'LOGIN': 'info',
    'LOGOUT': 'info',
    'PASSWORD_RESET': 'warning',
    'ENTITY_CREATE': 'success',
    'ENTITY_UPDATE': 'warning',
    'ENTITY_DELETE': 'danger'
  }
  return tagMap[actionType] || 'info'
}
</script>

<style scoped>
.audit-log-detail {
  max-height: 600px;
  overflow-y: auto;
}

.json-content {
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 12px;
  margin: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.mt-4 {
  margin-top: 16px;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-descriptions__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style> 