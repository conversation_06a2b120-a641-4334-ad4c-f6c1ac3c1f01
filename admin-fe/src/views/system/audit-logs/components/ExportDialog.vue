<template>
  <el-dialog
    v-model="dialogVisible"
    title="导出审计日志"
    width="500px"
    :before-close="handleClose"
  >
    <el-form :model="exportForm" label-width="120px">
      <el-form-item label="导出格式">
        <el-radio-group v-model="exportForm.format">
          <el-radio label="csv">CSV</el-radio>
          <el-radio label="excel">Excel</el-radio>
          <el-radio label="json">JSON</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="导出字段">
        <el-checkbox-group v-model="exportForm.fields">
          <el-checkbox label="id">日志ID</el-checkbox>
          <el-checkbox label="actionType">操作类型</el-checkbox>
          <el-checkbox label="actionTime">操作时间</el-checkbox>
          <el-checkbox label="actorUserId">操作者ID</el-checkbox>
          <el-checkbox label="actorInfo">操作者信息</el-checkbox>
          <el-checkbox label="affectedEntityId">受影响实体ID</el-checkbox>
          <el-checkbox label="affectedEntityInfo">受影响实体信息</el-checkbox>
          <el-checkbox label="details">操作详情</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="时间范围">
        <el-date-picker
          v-model="exportForm.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DDTHH:mm:ss.SSSZ"
        />
      </el-form-item>

      <el-form-item label="操作类型">
        <el-select
          v-model="exportForm.actionType"
          placeholder="请选择操作类型"
          clearable
        >
          <el-option label="全部" value="" />
          <el-option label="用户邀请" value="USER_INVITE" />
          <el-option label="角色分配" value="ROLE_ASSIGN" />
          <el-option label="停用账户" value="DEACTIVATION" />
          <el-option label="身份冒充" value="IMPERSONATION" />
          <el-option label="登录" value="LOGIN" />
          <el-option label="登出" value="LOGOUT" />
          <el-option label="密码重置" value="PASSWORD_RESET" />
          <el-option label="实体创建" value="ENTITY_CREATE" />
          <el-option label="实体更新" value="ENTITY_UPDATE" />
          <el-option label="实体删除" value="ENTITY_DELETE" />
        </el-select>
      </el-form-item>

      <el-form-item label="关键词">
        <el-input
          v-model="exportForm.keyword"
          placeholder="请输入关键词搜索"
          clearable
        />
      </el-form-item>

      <el-form-item label="最大导出数量">
        <el-input-number
          v-model="exportForm.maxCount"
          :min="1"
          :max="10000"
          placeholder="最大导出数量"
        />
        <span class="form-tip">建议不超过10000条</span>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleExport" :loading="exporting">
          开始导出
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { auditLogApi } from '@/api/auditLog'
import type { QueryAuditLogsParams } from '@/api/auditLog/types'

interface Props {
  visible: boolean
  searchParams: QueryAuditLogsParams
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'export-success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const exporting = reactive({
  value: false
})

const exportForm = reactive({
  format: 'excel',
  fields: ['id', 'actionType', 'actionTime', 'actorUserId', 'actorInfo', 'affectedEntityId', 'affectedEntityInfo', 'details'],
  timeRange: null as [string, string] | null,
  actionType: '',
  keyword: '',
  maxCount: 1000
})

const handleClose = () => {
  dialogVisible.value = false
}

const handleExport = async () => {
  if (exportForm.fields.length === 0) {
    ElMessage.warning('请至少选择一个导出字段')
    return
  }

  if (!exportForm.timeRange) {
    ElMessage.warning('请选择时间范围')
    return
  }

  try {
    exporting.value = true

    // 构建导出参数
    const params = {
      ...props.searchParams,
      format: exportForm.format,
      fields: exportForm.fields,
      timeRange: exportForm.timeRange,
      actionType: exportForm.actionType,
      keyword: exportForm.keyword,
      maxCount: exportForm.maxCount
    }

    const response = await auditLogApi.exportAuditLogs(params)
    
    // 创建下载链接
    const blob = new Blob([response], {
      type: getContentType(exportForm.format)
    })
    
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `audit-logs-${new Date().toISOString().slice(0, 10)}.${getFileExtension(exportForm.format)}`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

    ElMessage.success('导出成功')
    emit('export-success')
    handleClose()
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exporting.value = false
  }
}

const getContentType = (format: string) => {
  const contentTypeMap: Record<string, string> = {
    csv: 'text/csv',
    excel: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    json: 'application/json'
  }
  return contentTypeMap[format] || 'application/octet-stream'
}

const getFileExtension = (format: string) => {
  const extensionMap: Record<string, string> = {
    csv: 'csv',
    excel: 'xlsx',
    json: 'json'
  }
  return extensionMap[format] || 'txt'
}
</script>

<style scoped>
.form-tip {
  margin-left: 8px;
  font-size: 12px;
  color: #909399;
}

.dialog-footer {
  text-align: right;
}

:deep(.el-checkbox-group) {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

:deep(.el-form-item__content) {
  display: flex;
  align-items: center;
}
</style> 