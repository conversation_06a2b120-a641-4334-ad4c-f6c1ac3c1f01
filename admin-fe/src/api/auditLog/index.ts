import { request } from '@/utils/request'
import type { QueryAuditLogsParams, QueryAuditLogsResponse } from './types'

export const auditLogApi = {
  // 查询审计日志
  queryAuditLogs: (params: QueryAuditLogsParams): Promise<QueryAuditLogsResponse> => {
    return request({
      url: '/api/user/audit-logs',
      method: 'POST',
      data: params
    })
  },

  // 添加审计日志
  addAuditLog: (params: any): Promise<any> => {
    return request({
      url: '/api/user/audit-log',
      method: 'POST',
      data: params
    })
  },

  // 导出审计日志
  exportAuditLogs: (params: QueryAuditLogsParams): Promise<any> => {
    return request({
      url: '/api/user/audit-logs/export',
      method: 'POST',
      data: params,
      responseType: 'blob'
    })
  }
} 