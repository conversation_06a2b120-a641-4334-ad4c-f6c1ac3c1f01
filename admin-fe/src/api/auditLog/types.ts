// 审计日志查询参数
export interface QueryAuditLogsParams {
  page: number
  pageSize: number
  actionType?: string
  keyword?: string
  actorUserId?: number
  affectedEntityId?: number
  includeDetails?: boolean
  sortBy?: string
  sortOrder?: string
  actionTime?: {
    start: string
    end: string
  }
}

// 审计日志项
export interface AuditLogItem {
  id: number
  actionType: string
  actionTime: string
  actorUserId: number
  actorInfo: string
  affectedEntityId: number
  affectedEntityInfo: string
  details: any
}

// 审计日志统计信息
export interface AuditLogStatistics {
  totalCount: number
  actionTypeCount: Record<string, number>
  topActors: ActorStatistics[]
  topEntities: EntityStatistics[]
  timeDistribution: TimeDistributionItem[]
  recentActivities: RecentActivityItem[]
}

// 操作者统计
export interface ActorStatistics {
  actorUserId: number
  actionCount: number
  lastAction: string
}

// 实体统计
export interface EntityStatistics {
  entityId: number
  actionCount: number
  lastAction: string
}

// 时间分布项
export interface TimeDistributionItem {
  timeSlot: string
  count: number
}

// 最近活动项
export interface RecentActivityItem {
  actionType: string
  actorUserId: number
  affectedEntityId: number
  actionTime: string
}

// 审计日志查询响应
export interface QueryAuditLogsResponse {
  table: {
    total: number
    hasMore: boolean
    rows: AuditLogItem[]
    headerKeys: string[]
  }
  statistics?: AuditLogStatistics
} 