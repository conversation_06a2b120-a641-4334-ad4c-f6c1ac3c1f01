package service

import (
	"context"
	"hotel/trade/protocol"
)

// LabelOrder
// @desc: 订单打标签
// @tags: admin.hotelbyte.com
func (s *TradeService) LabelOrder(ctx context.Context, req *protocol.LabelOrderReq) (*protocol.LabelOrderResp, error) {
	err := s.orderDao.LabelOrder(ctx, req.OrderId, req.AddLabels, req.RemoveLabels)
	if err != nil {
		return nil, err
	}
	//todo: audit log
	return &protocol.LabelOrderResp{}, nil
}
