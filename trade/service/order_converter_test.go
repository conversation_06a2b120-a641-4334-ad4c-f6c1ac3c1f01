package service

import (
	"testing"
	"time"

	"hotel/common/types"
	"hotel/trade/dao"
	tradeDomain "hotel/trade/domain"
	"hotel/trade/protocol"

	"github.com/stretchr/testify/assert"
)

func TestConvertProtocolCriteriaToDAO(t *testing.T) {
	tests := []struct {
		name     string
		criteria protocol.QueryOrderCriteria
		expected *dao.OrderQueryCriteria
	}{
		{
			name: "转换平台订单ID",
			criteria: protocol.QueryOrderCriteria{
				PlatformOrderIds: []types.ID{types.ID(123), types.ID(456)},
			},
			expected: &dao.OrderQueryCriteria{
				PlatformOrderIds: []uint64{123, 456},
			},
		},
		{
			name: "转换外部订单号",
			criteria: protocol.QueryOrderCriteria{
				ReferenceNos: []string{"REF001", "REF002"},
			},
			expected: &dao.OrderQueryCriteria{
				ReferenceNos: []string{"REF001", "REF002"},
			},
		},
		{
			name: "转换状态列表",
			criteria: protocol.QueryOrderCriteria{
				StatusList: []tradeDomain.OrderStatus{
					tradeDomain.OrderStateCreated,
					tradeDomain.OrderStateConfirmed,
				},
			},
			expected: &dao.OrderQueryCriteria{
				StatusList: []int64{1, 4},
			},
		},
		{
			name: "转换时间窗口",
			criteria: protocol.QueryOrderCriteria{
				CreateTimeWindow: types.TimeWindow{
					Start: time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC),
					End:   time.Date(2024, 1, 31, 23, 59, 59, 0, time.UTC),
				},
			},
			expected: &dao.OrderQueryCriteria{
				CreateTimeWindow: &dao.TimeWindow{
					Start: &[]time.Time{time.Date(2024, 1, 1, 0, 0, 0, 0, time.UTC)}[0],
					End:   &[]time.Time{time.Date(2024, 1, 31, 23, 59, 59, 0, time.UTC)}[0],
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertProtocolCriteriaToDAO(tt.criteria)

			// 验证平台订单ID
			assert.Equal(t, tt.expected.PlatformOrderIds, result.PlatformOrderIds)

			// 验证外部订单号
			assert.Equal(t, tt.expected.ReferenceNos, result.ReferenceNos)

			// 验证状态列表
			assert.Equal(t, tt.expected.StatusList, result.StatusList)

			// 验证时间窗口
			if tt.expected.CreateTimeWindow != nil {
				assert.NotNil(t, result.CreateTimeWindow)
				if tt.expected.CreateTimeWindow.Start != nil {
					assert.Equal(t, *tt.expected.CreateTimeWindow.Start, *result.CreateTimeWindow.Start)
				}
				if tt.expected.CreateTimeWindow.End != nil {
					assert.Equal(t, *tt.expected.CreateTimeWindow.End, *result.CreateTimeWindow.End)
				}
			}
		})
	}
}

func TestConvertDAOOrderToDomain(t *testing.T) {
	tests := []struct {
		name     string
		daoOrder *dao.Order
		expected tradeDomain.Order
	}{
		{
			name: "转换已创建订单",
			daoOrder: &dao.Order{
				Id:          123,
				ReferenceNo: "REF001",
				Status:      int64(tradeDomain.OrderStateCreated),
				CreateTime:  time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
			expected: tradeDomain.Order{
				Id:          types.ID(123),
				ReferenceNo: "REF001",
				Status:      tradeDomain.OrderStateCreated,
				CreateTime:  time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
			},
		},
		{
			name: "转换已确认订单",
			daoOrder: &dao.Order{
				Id:          456,
				ReferenceNo: "REF002",
				Status:      int64(tradeDomain.OrderStateConfirmed),
				CreateTime:  time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
			},
			expected: tradeDomain.Order{
				Id:          types.ID(456),
				ReferenceNo: "REF002",
				Status:      tradeDomain.OrderStateConfirmed,
				CreateTime:  time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
			},
		},
		{
			name: "转换已取消订单",
			daoOrder: &dao.Order{
				Id:          789,
				ReferenceNo: "REF003",
				Status:      int64(tradeDomain.OrderStateCancelled),
				CreateTime:  time.Date(2024, 1, 3, 12, 0, 0, 0, time.UTC),
			},
			expected: tradeDomain.Order{
				Id:          types.ID(789),
				ReferenceNo: "REF003",
				Status:      tradeDomain.OrderStateCancelled,
				CreateTime:  time.Date(2024, 1, 3, 12, 0, 0, 0, time.UTC),
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := convertDAOOrderToDomain(tt.daoOrder)

			assert.Equal(t, tt.expected.Id, result.Id)
			assert.Equal(t, tt.expected.ReferenceNo, result.ReferenceNo)
			assert.Equal(t, tt.expected.Status, result.Status)
			assert.Equal(t, tt.expected.CreateTime, result.CreateTime)
		})
	}
}

func TestConvertDAOOrdersToDomain(t *testing.T) {
	daoOrders := []*dao.Order{
		{
			Id:          123,
			ReferenceNo: "REF001",
			Status:      int64(tradeDomain.OrderStateCreated),
			CreateTime:  time.Date(2024, 1, 1, 12, 0, 0, 0, time.UTC),
		},
		{
			Id:          456,
			ReferenceNo: "REF002",
			Status:      int64(tradeDomain.OrderStateConfirmed),
			CreateTime:  time.Date(2024, 1, 2, 12, 0, 0, 0, time.UTC),
		},
	}

	result := convertDAOOrdersToDomain(daoOrders)

	assert.Len(t, result, 2)
	assert.Equal(t, types.ID(123), result[0].Id)
	assert.Equal(t, "REF001", result[0].ReferenceNo)
	assert.Equal(t, tradeDomain.OrderStateCreated, result[0].Status)

	assert.Equal(t, types.ID(456), result[1].Id)
	assert.Equal(t, "REF002", result[1].ReferenceNo)
	assert.Equal(t, tradeDomain.OrderStateConfirmed, result[1].Status)
}
