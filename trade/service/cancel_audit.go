package service

import (
	"context"
	"encoding/json"
	"hotel/common/types"
	"time"

	"hotel/common/log"
	"hotel/trade/domain"
)

// CancelAuditEvent 取消操作审计事件
type CancelAuditEvent struct {
	OrderID               types.ID               `json:"order_id"`
	Action                string                 `json:"action"`
	Timestamp             time.Time              `json:"timestamp"`
	UserID                types.ID               `json:"user_id,omitempty"`
	OriginalStatus        domain.OrderStatus     `json:"original_status"`
	FinalStatus           domain.OrderStatus     `json:"final_status"`
	SupplierCancelSuccess bool                   `json:"supplier_cancel_success"`
	SupplierErrors        []string               `json:"supplier_errors,omitempty"`
	Reason                string                 `json:"reason"`
	Metadata              map[string]interface{} `json:"metadata,omitempty"`
}

// CancelMetrics 取消操作监控指标
type CancelMetrics struct {
	TotalCancelRequests     int64 `json:"total_cancel_requests"`
	SuccessfulCancellations int64 `json:"successful_cancellations"`
	FailedCancellations     int64 `json:"failed_cancellations"`
	SupplierCancelFailures  int64 `json:"supplier_cancel_failures"`
	DatabaseUpdateFailures  int64 `json:"database_update_failures"`
	MessageQueueFailures    int64 `json:"message_queue_failures"`
	AverageProcessingTimeMs int64 `json:"average_processing_time_ms"`
}

// CancelAuditor 取消操作审计器
type CancelAuditor struct {
	tradeService *TradeService
	metrics      *CancelMetrics
}

// NewCancelAuditor 创建取消操作审计器;todo：移除，直接在 TradeService 中实现
func NewCancelAuditor(tradeService *TradeService) *CancelAuditor {
	return &CancelAuditor{
		tradeService: tradeService,
		metrics:      &CancelMetrics{},
	}
}

// RecordCancelRequest 记录取消请求
func (a *CancelAuditor) RecordCancelRequest(ctx context.Context, orderID types.ID, userID types.ID, reason string) {
	a.metrics.TotalCancelRequests++

	event := &CancelAuditEvent{
		OrderID:   orderID,
		Action:    "cancel_request",
		Timestamp: time.Now(),
		UserID:    userID,
		Reason:    reason,
		Metadata: map[string]interface{}{
			"source": "api",
		},
	}

	a.logAuditEvent(ctx, event)
}

// RecordCancelSuccess 记录取消成功
func (a *CancelAuditor) RecordCancelSuccess(ctx context.Context, order *domain.Order, originalStatus domain.OrderStatus, supplierCancelSuccess bool, processingTimeMs int64) {
	a.metrics.SuccessfulCancellations++

	if !supplierCancelSuccess {
		a.metrics.SupplierCancelFailures++
	}

	// 更新平均处理时间
	a.updateAverageProcessingTime(processingTimeMs)

	event := &CancelAuditEvent{
		OrderID:               order.Id,
		Action:                "cancel_success",
		Timestamp:             time.Now(),
		OriginalStatus:        originalStatus,
		FinalStatus:           domain.OrderStateCancelled,
		SupplierCancelSuccess: supplierCancelSuccess,
		Metadata: map[string]interface{}{
			"processing_time_ms": processingTimeMs,
		},
	}

	a.logAuditEvent(ctx, event)
}

// RecordCancelFailure 记录取消失败
func (a *CancelAuditor) RecordCancelFailure(ctx context.Context, orderID types.ID, originalStatus domain.OrderStatus, failureType string, errorMsg string, processingTimeMs int64) {
	a.metrics.FailedCancellations++

	switch failureType {
	case "supplier":
		a.metrics.SupplierCancelFailures++
	case "database":
		a.metrics.DatabaseUpdateFailures++
	case "message_queue":
		a.metrics.MessageQueueFailures++
	}

	event := &CancelAuditEvent{
		OrderID:        orderID,
		Action:         "cancel_failure",
		Timestamp:      time.Now(),
		OriginalStatus: originalStatus,
		FinalStatus:    originalStatus, // 失败时状态不变
		Metadata: map[string]interface{}{
			"failure_type":       failureType,
			"error_message":      errorMsg,
			"processing_time_ms": processingTimeMs,
		},
	}

	a.logAuditEvent(ctx, event)
}

// RecordSupplierCancelError 记录供应商取消错误
func (a *CancelAuditor) RecordSupplierCancelError(ctx context.Context, orderID types.ID, supplierType string, errorMsg string) {
	event := &CancelAuditEvent{
		OrderID:        orderID,
		Action:         "supplier_cancel_error",
		Timestamp:      time.Now(),
		SupplierErrors: []string{errorMsg},
		Metadata: map[string]interface{}{
			"supplier_type": supplierType,
		},
	}

	a.logAuditEvent(ctx, event)
}

// GetMetrics 获取监控指标
func (a *CancelAuditor) GetMetrics() *CancelMetrics {
	return a.metrics
}

// ResetMetrics 重置监控指标
func (a *CancelAuditor) ResetMetrics() {
	a.metrics = &CancelMetrics{}
}

// logAuditEvent 记录审计事件
func (a *CancelAuditor) logAuditEvent(ctx context.Context, event *CancelAuditEvent) {
	eventJSON, err := json.Marshal(event)
	if err != nil {
		log.Errorc(ctx, "Failed to marshal audit event: %v", err)
		return
	}

	// 记录到日志
	log.Infoc(ctx, "AUDIT_EVENT: %s", string(eventJSON))

	// TODO: 在实际项目中，这里应该：
	// 1. 写入专门的审计日志表
	// 2. 发送到审计日志系统（如ELK）
	// 3. 发送到监控系统（如Prometheus）
	// 4. 发送到告警系统
}

// updateAverageProcessingTime 更新平均处理时间
func (a *CancelAuditor) updateAverageProcessingTime(newTimeMs int64) {
	if a.metrics.TotalCancelRequests == 1 {
		a.metrics.AverageProcessingTimeMs = newTimeMs
	} else {
		// 简单的移动平均
		a.metrics.AverageProcessingTimeMs = (a.metrics.AverageProcessingTimeMs + newTimeMs) / 2
	}
}

// MonitorCancelOperations 监控取消操作的健康状况
func (a *CancelAuditor) MonitorCancelOperations(ctx context.Context) {
	metrics := a.GetMetrics()

	// 计算成功率
	var successRate float64
	if metrics.TotalCancelRequests > 0 {
		successRate = float64(metrics.SuccessfulCancellations) / float64(metrics.TotalCancelRequests) * 100
	}

	// 记录监控指标
	log.Infoc(ctx, "Cancel Operations Metrics: Total=%d, Success=%d, Failed=%d, SuccessRate=%.2f%%, AvgProcessingTime=%dms",
		metrics.TotalCancelRequests,
		metrics.SuccessfulCancellations,
		metrics.FailedCancellations,
		successRate,
		metrics.AverageProcessingTimeMs,
	)

	// 检查是否需要告警
	if successRate < 95.0 && metrics.TotalCancelRequests > 10 {
		log.Warnc(ctx, "ALERT: Cancel operation success rate is below 95%: %.2f%%", successRate)
	}

	if metrics.AverageProcessingTimeMs > 5000 {
		log.Warnc(ctx, "ALERT: Average cancel processing time is above 5 seconds: %dms", metrics.AverageProcessingTimeMs)
	}
}
