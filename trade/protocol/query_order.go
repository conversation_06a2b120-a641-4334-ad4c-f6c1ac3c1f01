package protocol

import (
	"hotel/common/bff"
	"hotel/common/pagehelper"
	"hotel/common/types"
	searchDomain "hotel/content/domain"
	geoDomain "hotel/geography/domain"
	"hotel/trade/domain"
	userDomain "hotel/user/domain"
)

type QueryOrdersReq struct {
	QueryOrderCriteria
}
type QueryOrdersResp struct {
	Orders []domain.Order `json:"orders"`
}

type QueryOrderCriteria struct {
	PlatformOrderIds   types.IDs            `json:"platformOrderIds"`
	ReferenceNos       []string             `json:"referenceNos"`
	CheckInTimeWindow  types.TimeWindow     `json:"checkInTimeWindow"`
	CheckOutTimeWindow types.TimeWindow     `json:"checkOutTimeWindow"`
	CreateTimeWindow   types.TimeWindow     `json:"createTimeWindow"`
	CancelTimeWindow   types.TimeWindow     `json:"cancelTimeWindow"`
	StatusList         []domain.OrderStatus `json:"statusList"`
	Tags               []string             `json:"tags"`
}

type ListOrderReq struct {
	QueryOrderCriteria
	InternalKeyword string `json:"keyword"`
	pagehelper.PageReq
}
type ListOrderResp struct {
	bff.Table[domain.Order] // @generic: Order
}

type DetailOrderReq struct {
	OrderId types.ID `json:"orderId"`
}
type DetailOrderResp struct {
	Summary   *domain.OrderSummary   `json:"summary"`
	Account   *DetailOrderAccount    `json:"account"`
	SubOrders []*DetailOrderSubOrder `json:"subOrders"`
}

type DetailOrderSubOrder struct {
	Hotel   *DetailOrderHotel                  `json:"hotel"`
	Booking bff.Table[domain.OrderSummary]     `json:"booking"`
	Gain    bff.Table[domain.OrderGainSummary] `json:"gain"`
}

type DetailOrderHotel struct {
	*searchDomain.HotelSummary
	ConfirmNumber string `json:"confirmNumber"` // 酒店确认号
	OrderId       string `json:"orderId"`       // 平台订单号
}
type DetailOrderAccount struct {
	*domain.OrderAccount
	CustomerBuyer bff.Table[userDomain.Entity] `json:"customerBuyer,omitzero"`
}

type OrderHomeFunctionReq struct {
}
type OrderHomeFunctionResp struct {
	Tags                 []string                  `json:"tags"`
	CountryOrderCounters []geoDomain.RegionCounter `json:"countryOrderCounters"`
	RecommendedOrders    bff.Table[domain.Order]   `json:"recommendedOrders"` // @generic: Order; 默认展示
}
