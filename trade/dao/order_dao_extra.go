package dao

import (
	"context"
	"hotel/common/types"
	"hotel/common/utils"
	"hotel/trade/domain"
	"slices"
)

// BatchGetOrders fetch orders by ids
func (d *OrderDao) BatchGetOrders(ctx context.Context, ids []uint64) ([]*Order, error) {
	return d.Order.FindByIds(ctx, ids)
}

func (d *OrderDao) GetOrder(ctx context.Context, id types.ID) (*domain.Order, error) {
	order, err := d.Order.FindOne(ctx, uint64(id.Int64()))
	if err != nil {
		return nil, err
	}
	return ConvertOrderToDomain(order), nil
}

func (d *OrderDao) LabelOrder(ctx context.Context, id types.ID, addLabels, removeLabels []string) error {
	order, err := d.Order.FindOne(ctx, uint64(id.Int64()))
	if err != nil {
		return err
	}
	bizInfo := utils.FromJSON[domain.OrderBizInfo](order.BizInfo)
	bizInfo.Label = append(bizInfo.Label, addLabels...)
	bizInfo.Label = utils.Deduplicate(bizInfo.Label)
	bizInfo.Label = utils.Filter(bizInfo.Label, func(label string) bool {
		return !slices.Contains(removeLabels, label)
	})
	order.BizInfo = utils.ToJSON(bizInfo)
	return d.Order.Update(ctx, order)
}

func ConvertOrderToDomain(order *Order) *domain.Order {
	return &domain.Order{
		Id:          types.ID(order.Id),
		ReferenceNo: order.ReferenceNo,
		Status:      domain.OrderStatus(order.Status),
		//todo:
	}
}

func ConvertOrderToModel(order *domain.Order) *Order {
	return &Order{
		Id:          uint64(order.Id.Int64()),
		ReferenceNo: order.ReferenceNo,
		UserId:      uint64(order.OrderAccount.CustomerAccount.ID.Int64()),
		Status:      order.Status.Int64(),

		//todd: more fields
		BizInfo: utils.ToJSON(order.BizInfo),
	}
}
